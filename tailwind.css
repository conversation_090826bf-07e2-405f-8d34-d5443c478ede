@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .all-\[unset\] {
    all: unset;
  }

  /* Custom scrollbar styles for horizontal certificate scroll */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Optional: Add subtle scroll indicators */
  .scrollbar-hide::before,
  .scrollbar-hide::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 1;
  }

  .scrollbar-hide::before {
    left: 0;
    background: linear-gradient(to right, rgba(255,255,255,0.8), transparent);
  }

  .scrollbar-hide::after {
    right: 0;
    background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
  }

  /* Animation for tab content switching */
  @keyframes fadeInSlide {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeOutSlide {
    0% {
      opacity: 1;
      transform: translateY(0);
    }
    100% {
      opacity: 0;
      transform: translateY(-10px);
    }
  }

  /* Enhanced tab switching animations */
  @keyframes iconGlow {
    0% {
      box-shadow: 0 0 0 rgba(91, 61, 252, 0);
    }
    50% {
      box-shadow: 0 0 20px rgba(91, 61, 252, 0.4);
    }
    100% {
      box-shadow: 0 2px 8px rgba(91, 61, 252, 0.3);
    }
  }

  @keyframes backgroundSlide {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes pulseScale {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1.1);
    }
  }

  /* Smooth content transitions */
  .content-transition {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .content-fade-in {
    animation: fadeInSlide 0.5s ease-out forwards;
  }

  .content-fade-out {
    animation: fadeOutSlide 0.3s ease-in forwards;
  }

  /* Tab-specific animations */
  .tab-icon-active {
    animation: iconGlow 0.4s ease-out forwards, pulseScale 0.4s ease-out forwards;
  }

  .tab-background-slide {
    animation: backgroundSlide 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  /* Enhanced hover effects */
  .tab-hover-effect:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease-out;
  }

  .tab-icon-hover:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease-out;
  }
}

:root {
  --accent-colordefault: rgba(133, 69, 247, 1);
  --text-color: rgba(15, 0, 26, 1);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: transparent;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: transparent;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}
