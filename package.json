{"version": "1.0.0", "source": "./index.html", "type": "module", "name": "anima-project", "description": "A React project automatically generated by <PERSON><PERSON> using the Shadcn UI library", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"clsx": "2.1.1", "lucide-react": "^0.453.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "tailwind-merge": "2.5.4", "@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-toggle": "^1.1.0"}, "devDependencies": {"@animaapp/vite-plugin-screen-graph": "^0.1.5", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@vitejs/plugin-react": "4.3.4", "esbuild": "0.24.0", "globals": "15.12.0", "tailwindcss": "3.4.16", "vite": "6.0.4"}, "alias": {"@/*": "./src/components/ui/$1"}}