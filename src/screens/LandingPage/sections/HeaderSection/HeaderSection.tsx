import { Button } from "../../../../components/ui/button";

// Component Props Interface
interface HeaderSectionProps {
  title?: string;
  subtitle?: string;
  ctaText?: string;
  ctaColor?: string;
  studentImageSrc?: string;
  studentImageAlt?: string;
  imageWidth?: string;
  imageHeight?: string;
  imageTop?: string;
  imageLeft?: string;
}

// Small Circle Component
const SmallCircle = () => (
  <svg width="35" height="35" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g style={{ mixBlendMode: "luminosity" }}>
      <path d="M34.5319 16.3241C34.5319 16.3141 34.5319 16.2941 34.5319 16.2841C34.5319 16.2041 34.5019 15.7341 34.4119 15.0241C34.4019 14.9641 34.3919 14.9041 34.3819 14.8341C34.3719 14.7441 34.3519 14.6541 34.3419 14.5641C33.9819 12.3041 33.1719 10.1641 32.0019 8.27414L31.9919 8.26414C31.8619 8.05414 31.7319 7.85414 31.6019 7.65414C30.9519 6.69414 30.2019 5.79414 29.3719 4.98414C28.9819 4.59414 28.5019 4.17414 28.0819 3.84414C27.8019 3.62414 27.5119 3.40414 27.2219 3.19414C27.2119 3.18414 27.1919 3.17414 27.1819 3.16414L27.1419 3.13414C27.0919 3.09414 27.0419 3.06414 26.9919 3.02414C26.8019 2.89414 26.5919 2.75414 26.3719 2.62414C26.3619 2.61414 26.3519 2.61414 26.3419 2.60414C26.2319 2.53414 26.1219 2.47414 26.0019 2.40414C25.9519 2.37414 25.8919 2.34414 25.8419 2.31414C25.7919 2.28414 25.7519 2.26414 25.7019 2.23414C25.6619 2.21414 25.6319 2.19414 25.5919 2.17414C24.9119 1.80414 24.2019 1.47414 23.4719 1.19414C23.2819 1.12414 23.1319 1.06414 23.0519 1.03414L22.9219 0.984142L22.7919 0.934143C22.7819 0.934143 22.7819 0.934141 22.7719 0.924141L22.7419 0.914142C22.6819 0.894142 22.5919 0.864143 22.5319 0.844143L22.5019 0.83414C22.4919 0.83414 22.4819 0.824142 22.4719 0.824142L22.3219 0.774143C22.2619 0.754143 22.1819 0.734142 22.1319 0.714142L21.9619 0.664142C21.9319 0.654142 21.9019 0.644144 21.8719 0.634144C21.8619 0.634144 21.8519 0.624141 21.8419 0.624141C21.8319 0.624141 21.8219 0.614143 21.8119 0.614143C21.7919 0.604143 21.7719 0.604141 21.7619 0.604141L21.5719 0.554142C21.5219 0.544142 21.4619 0.524141 21.4119 0.514141C21.3619 0.504141 21.3119 0.494144 21.2619 0.474144H21.2519C21.2219 0.464144 21.1919 0.464143 21.1619 0.454143C21.1019 0.444143 21.0319 0.424142 20.9819 0.414142L20.8519 0.384144C20.7919 0.374144 20.7319 0.364143 20.6819 0.344143C20.6719 0.344143 20.6619 0.34414 20.6519 0.334141C20.6119 0.324141 20.5619 0.314142 20.5219 0.304142L20.4419 0.294143L20.3519 0.274143C20.3419 0.274143 20.3419 0.274143 20.3319 0.274143L20.3019 0.264141C20.2819 0.264141 20.2619 0.254142 20.2519 0.254142C20.2019 0.244142 20.1619 0.234142 20.1119 0.234142C20.0819 0.224142 20.0519 0.224142 20.0219 0.214142C19.9719 0.204142 19.9219 0.194141 19.8619 0.194141L19.7319 0.174141C19.7119 0.174141 19.6819 0.164142 19.6519 0.164142C19.6119 0.154142 19.5719 0.154142 19.5319 0.144142C19.4919 0.134142 19.4519 0.134141 19.4119 0.124141C19.3519 0.114141 19.2819 0.104141 19.2219 0.104141L19.0419 0.0841405C19.0219 0.0841405 19.0019 0.0841422 18.9819 0.0741422H18.9719C18.9219 0.0741422 18.8619 0.0641439 18.8119 0.0641439C18.7319 0.0541439 18.6519 0.0541434 18.5719 0.0441434C18.5119 0.0441434 18.4519 0.0341413 18.4019 0.0341413C18.3419 0.0341413 18.2819 0.0241429 18.2119 0.0241429C18.1519 0.0241429 18.0819 0.0141408 18.0219 0.0141408C17.9819 0.0141408 17.9419 0.0141408 17.9019 0.0141408L17.7919 0.00414248C17.7619 0.00414248 17.7419 0.00414248 17.7119 0.00414248C17.6819 0.00414248 17.6519 0.00414248 17.6219 0.00414248C17.2719 -0.00585752 16.9319 0.0041408 16.5819 0.0141408C16.5019 0.0141408 16.4319 0.0241429 16.3619 0.0241429C16.2819 0.0241429 16.2119 0.0341413 16.1319 0.0341413C16.0819 0.0341413 16.0319 0.0441434 15.9919 0.0441434C15.8819 0.0541434 15.7719 0.0641422 15.6619 0.0741422C15.6219 0.0741422 15.5819 0.0841405 15.5419 0.0841405C15.3819 0.104141 15.2319 0.114144 15.0819 0.134144C15.0119 0.144144 14.9419 0.154142 14.8719 0.164142C14.8719 0.164142 14.8819 0.164142 14.8919 0.164142C14.8819 0.164142 14.8819 0.164142 14.8719 0.164142C14.8619 0.164142 14.8419 0.164141 14.8319 0.174141C14.7119 0.194141 14.6119 0.204144 14.5019 0.224144C14.4219 0.234144 14.3319 0.254141 14.2519 0.264141C14.0619 0.294141 13.9119 0.324143 13.8119 0.344143C13.3119 0.444143 12.8219 0.574142 12.3319 0.714142C12.0419 0.794142 11.7819 0.884144 11.4619 0.994144C10.1319 1.47414 8.85192 2.11414 7.66192 2.91414C7.54192 2.99414 7.42193 3.07414 7.31193 3.15414C7.29193 3.16414 7.28193 3.17414 7.26193 3.18414C7.06193 3.32414 6.86192 3.47414 6.66192 3.63414C6.64192 3.64414 6.63194 3.65414 6.61194 3.67414L6.59193 3.68414C6.59193 3.68414 6.58193 3.69414 6.57193 3.69414C6.56193 3.70414 6.55192 3.70414 6.55192 3.71414C6.54192 3.71414 6.54193 3.72414 6.53193 3.72414C6.48193 3.76414 6.42193 3.81414 6.37193 3.85414C6.26193 3.94414 6.16193 4.03414 6.06193 4.11414C6.00193 4.16414 5.95193 4.21414 5.89193 4.26414C5.77193 4.36414 5.67193 4.46414 5.56193 4.55414C5.56193 4.55414 5.55193 4.56414 5.54193 4.56414L5.53193 4.57414C5.51193 4.59414 5.48193 4.61414 5.46193 4.64414C4.92193 5.14414 4.60194 5.50414 4.58194 5.53414C4.41194 5.71414 4.25193 5.90414 4.09193 6.09414C3.98193 6.22414 3.86193 6.37414 3.74193 6.52414C3.73193 6.53414 3.72193 6.54414 3.71193 6.55414L3.69192 6.57414C3.45192 6.87414 3.20193 7.22414 2.92193 7.63414L2.91192 7.64414C2.91192 7.64414 2.91193 7.65414 2.90193 7.65414C2.90193 7.66414 2.89193 7.66414 2.89193 7.67414C2.76193 7.86414 2.63194 8.07414 2.50194 8.29414C2.39194 8.48414 2.27193 8.67414 2.18193 8.83414L1.86194 9.47414C1.75194 9.68414 1.65193 9.88414 1.56193 10.0641C1.39193 10.4141 1.20192 10.8841 1.02192 11.3841V11.3941C0.871925 11.8241 0.721926 12.2841 0.601926 12.7241V12.7441C0.601926 12.7541 0.601926 12.7541 0.601926 12.7641C0.441926 13.3341 0.32193 13.8841 0.26193 14.3041C0.03193 15.9041 0.0219355 16.0341 0.00193547 17.0941C-0.0180645 18.4241 0.121923 19.3741 0.191923 19.8341C0.381923 21.1541 0.701933 22.1541 0.811933 22.4841C1.18193 23.6541 1.51194 24.3441 1.83194 25.0041C2.03194 25.4141 2.18193 25.7041 2.38193 26.0241C2.62193 26.4241 2.88193 26.8241 3.15193 27.2141C3.18193 27.2641 3.21194 27.3041 3.25194 27.3541C3.52194 27.7641 3.93192 28.2741 4.27192 28.6341C4.52192 28.9041 4.84193 29.2641 5.10193 29.5241C5.36193 29.7841 5.70193 30.1141 5.98193 30.3441C6.29193 30.6141 6.41193 30.7241 6.70193 30.9341C7.05193 31.2141 7.41192 31.4641 7.77192 31.7041C7.89192 31.7841 8.00193 31.8541 8.12193 31.9341C8.26193 32.0241 8.40192 32.1041 8.52192 32.1741C8.59192 32.2141 8.68193 32.2641 8.78193 32.3241C8.80193 32.3341 8.81194 32.3441 8.83194 32.3541C8.94194 32.4141 9.07193 32.4841 9.21193 32.5541C9.24193 32.5741 9.27192 32.5841 9.30192 32.6041C9.72192 32.8241 10.2519 33.0741 10.8319 33.3141C10.9619 33.3641 11.1319 33.4341 11.3419 33.5141C11.4019 33.5341 11.4619 33.5641 11.5119 33.5841L11.8219 33.6941C11.8819 33.7141 11.9719 33.7441 12.0319 33.7641L12.1119 33.7841C12.1219 33.7841 12.1219 33.7841 12.1319 33.7841L12.2519 33.8241C12.3119 33.8441 12.3919 33.8641 12.4419 33.8841L12.6319 33.9341C12.6419 33.9341 12.6419 33.9341 12.6519 33.9341C13.3919 34.1441 14.1019 34.2941 14.7819 34.3841L14.8419 34.3941C14.9019 34.4041 14.9619 34.4141 15.0319 34.4241C15.0419 34.4241 15.0419 34.4241 15.0519 34.4241C15.1119 34.4341 15.1719 34.4441 15.2219 34.4441L15.6019 34.4841C15.7719 34.5041 16.0019 34.5141 16.1719 34.5341C16.3319 34.5441 16.5219 34.5541 16.7119 34.5641C16.8019 34.5641 16.8919 34.5741 16.9719 34.5741C17.2119 34.5741 17.5319 34.5741 17.7619 34.5741C18.0019 34.5741 18.3319 34.5441 18.5719 34.5341C19.0919 34.4841 19.7819 34.4041 20.2919 34.3141C20.6919 34.2441 21.2219 34.1341 21.6119 34.0241C21.7119 33.9941 21.8119 33.9641 21.9219 33.9341C22.2319 33.8441 22.5519 33.7441 22.8119 33.6641L23.1219 33.5441C23.3719 33.4541 23.6219 33.3641 23.8619 33.2641L23.9819 33.2141C24.2719 33.1041 24.6219 32.9341 24.9319 32.7741C25.0619 32.7041 25.2019 32.6441 25.3319 32.5741C25.6919 32.3941 26.1319 32.1441 26.4519 31.9341C26.4619 31.9241 26.4719 31.9141 26.4819 31.9141C26.7819 31.7241 27.0719 31.5341 27.3519 31.3241C27.4119 31.2841 27.4819 31.2341 27.5319 31.1941L27.8419 30.9441C27.9219 30.8741 28.0119 30.8141 28.0919 30.7441L28.5619 30.3641C28.7419 30.2241 29.0619 29.9341 29.5419 29.4541C29.5519 29.4441 29.5619 29.4341 29.5619 29.4341L29.6119 29.3841C29.8619 29.1241 30.1419 28.8241 30.4719 28.4541C30.4819 28.4441 30.4819 28.4341 30.4919 28.4241C30.8119 28.0541 31.3519 27.3941 31.9319 26.4541C31.9319 26.4441 31.9419 26.4341 31.9419 26.4341C32.0719 26.2241 32.2119 25.9941 32.3419 25.7441C32.4019 25.6441 32.4519 25.5341 32.5119 25.4241C32.5919 25.2741 32.6719 25.1341 32.7319 24.9941C32.7519 24.9541 32.7719 24.9041 32.7919 24.8641C32.9019 24.6341 33.0119 24.3941 33.1219 24.1441C33.2719 23.8041 33.4119 23.4641 33.5119 23.1641C33.6919 22.6541 33.8619 22.1241 34.0119 21.5541C34.1019 21.2041 34.1819 20.8641 34.2419 20.5341C34.3119 20.1841 34.3619 19.8241 34.4119 19.4541C34.4219 19.3741 34.4319 19.2841 34.4419 19.2041C34.5019 18.7041 34.5319 18.1941 34.5419 17.6641C34.5419 17.6441 34.5419 17.6241 34.5419 17.6041V17.5641C34.5619 17.1841 34.5619 16.7641 34.5319 16.3241Z" fill="url(#paint0_radial_11_49)"/>
    </g>
    <defs>
      <radialGradient id="paint0_radial_11_49" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(16.7742 19.393) rotate(-0.355229) scale(19.0225 19.0225)">
        <stop offset="0.6517" stopColor="#FB9018"/>
        <stop offset="0.7401" stopColor="#FB931B"/>
        <stop offset="0.8094" stopColor="#FB9C25"/>
        <stop offset="0.8723" stopColor="#FBAB36"/>
        <stop offset="0.9313" stopColor="#FBC14D"/>
        <stop offset="0.9872" stopColor="#FBDD6B"/>
        <stop offset="1" stopColor="#FBE473"/>
      </radialGradient>
    </defs>
  </svg>
);

// Left Coin Component
const CoinLeft = () => (
  <svg width="176" height="197" viewBox="0 0 176 197" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g style={{ mixBlendMode: "luminosity" }} filter="url(#filter0_d_11_40)">
      <path d="M124.936 126.018C119.792 141.31 97.5231 137.945 75.1923 118.494C52.8615 99.0434 38.924 70.8835 44.0677 55.583C49.2115 40.2902 71.4807 43.663 93.8116 63.1139C116.142 82.5648 130.08 110.725 124.936 126.018Z" fill="url(#paint0_linear_11_40)"/>
      <path opacity="0.7" d="M93.8116 63.1217C71.4807 43.6708 49.2115 40.2981 44.0677 55.5908C38.924 70.8836 52.8615 99.0512 75.1923 118.502C97.5231 137.953 119.792 141.318 124.936 126.025C130.08 110.725 116.142 82.5649 93.8116 63.1217ZM124.567 125.81C122.28 132.609 116.397 135.897 107.996 135.081C98.5088 134.157 86.6272 128.004 75.3848 118.217C53.2388 98.928 39.3629 70.9298 44.4451 55.7987C46.732 48.9994 52.6151 45.7114 61.0161 46.5353C70.5028 47.4593 82.3921 53.6118 93.6345 63.3989C115.773 82.6881 129.649 110.686 124.567 125.81Z" fill="url(#paint1_linear_11_40)"/>
      <path d="M80.578 102.208L83.6274 108.352L78.7608 107.598L75.7038 101.453L80.578 102.208ZM93.0987 80.1771L90.0494 74.0323L85.1751 73.2777L88.2321 79.4302L93.0987 80.1771ZM120.204 121.997C115.684 135.442 95.9478 132.4 76.2043 115.206C56.4607 98.011 44.0787 73.0775 48.5988 59.6328C53.1188 46.1881 72.8624 49.2451 92.5982 66.4398C112.349 83.6346 124.724 108.553 120.204 121.997ZM91.859 68.6575C73.9096 53.0259 55.9679 50.2461 51.856 62.4665C47.744 74.6868 59.0096 97.3565 76.9512 112.988C94.9006 128.62 112.842 131.384 116.947 119.164C121.058 106.943 109.808 84.2814 91.859 68.6575ZM97.3416 90.5571C89.8954 84.0735 86.2146 84.805 82.7033 85.8291C80.27 86.4606 78.576 87.9159 75.6037 85.3286C74.0405 83.9657 73.5169 82.3178 73.8634 81.2937C74.2407 80.1617 76.6124 80.3388 80.3624 82.7798C80.4471 82.6489 80.555 82.4256 80.6551 82.1099C81.017 81.0242 81.4251 78.9297 81.4482 76.5272C78.7454 74.1709 69.3741 66.0086 68.196 65.0846C68.196 65.0846 66.3325 69.235 66.1631 69.3197C66.1477 69.3582 68.2191 71.2448 69.3356 72.3074C68.658 72.1303 67.295 72.1842 66.0861 73.1468C63.8299 71.2679 60.3802 68.5035 60.3571 68.5728C60.3417 68.6267 59.6255 70.3131 59.3021 71.291C59.1789 71.6606 59.1096 71.9301 59.1558 71.9686C59.3098 72.0995 62.0512 74.579 64.2535 76.4117C63.4218 80.0924 66.602 87.0304 72.2849 91.9817C78.6915 97.5567 82.3491 97.3026 86.1222 96.5171C89.2948 95.8857 91.3354 94.2225 93.2142 95.8626C95.2933 97.6722 95.3241 99.397 95.1085 100.036C94.7388 101.137 93.1834 101.338 90.1033 98.8811C90.0263 98.8118 89.7183 99.5202 89.4026 100.467C88.8559 102.092 88.2706 104.441 88.7634 104.864C90.4344 106.32 100.275 115.675 100.445 115.591C101.654 115.029 102.401 113.227 102.724 112.264C102.847 111.887 102.909 111.64 102.909 111.64L100.491 109.215C101.13 109.315 101.962 109.076 102.678 108.506C104.049 109.654 108.122 113.157 108.391 113.35C108.445 113.388 108.507 113.288 108.569 113.096C108.838 112.295 109.146 109.923 108.938 109.708C108.746 109.492 105.696 106.928 104.28 105.734C105.465 101.068 102.293 94.8616 97.3416 90.5571Z" fill="url(#paint2_linear_11_40)"/>
      <path d="M81.1445 101.793L84.1938 107.937L79.3272 107.183L76.2702 101.038L81.1445 101.793ZM93.6651 79.7621L90.6158 73.6173L85.7415 72.8626L88.7985 79.0152L93.6651 79.7621ZM120.77 121.582C116.25 135.027 96.5142 131.985 76.7707 114.791C57.0271 97.596 44.6451 72.6624 49.1652 59.2177C53.6852 45.773 73.4288 48.83 93.1646 66.0248C112.916 83.2118 125.29 108.138 120.77 121.582ZM92.4254 68.2348C74.476 52.6032 56.5343 49.8234 52.4224 62.0437C48.3104 74.2641 59.576 96.9337 77.5176 112.565C95.467 128.197 113.409 130.961 117.513 118.741C121.625 106.521 110.375 83.8663 92.4254 68.2348ZM97.908 90.1344C90.4618 83.6507 86.7811 84.3823 83.2697 85.4064C80.8364 86.0378 79.1424 87.4932 76.1701 84.9059C74.6069 83.5429 74.0833 81.8951 74.4298 80.8709C74.8071 79.739 77.1788 79.9161 80.9289 82.3571C81.0136 82.2262 81.1214 82.0029 81.2215 81.6872C81.5834 80.6014 81.9915 78.5069 82.0146 76.1045C79.3118 73.7482 69.9406 65.5859 68.7624 64.6618C68.7624 64.6618 66.8989 68.8123 66.7295 68.897C66.7141 68.9355 68.7855 70.8221 69.902 71.8847C69.2244 71.7076 67.8615 71.7615 66.6525 72.724C64.3964 70.8452 60.9466 68.0808 60.9235 68.1501C60.9081 68.204 60.192 69.8903 59.8686 70.8683C59.7454 71.2379 59.6761 71.5074 59.7223 71.5459C59.8763 71.6768 62.6176 74.1563 64.8199 75.989C63.9882 79.6697 67.1684 86.6076 72.8513 91.5589C79.2579 97.1339 82.9155 96.8798 86.6886 96.0944C89.8612 95.463 91.9018 93.7997 93.7806 95.4399C95.8597 97.2494 95.8905 98.9743 95.6749 99.6134C95.3053 100.715 93.7498 100.915 90.6697 98.4584C90.5927 98.3891 90.2847 99.0975 89.969 100.045C89.4223 101.669 88.837 104.018 89.3298 104.442C91.0008 105.897 100.842 115.253 101.011 115.168C102.22 114.606 102.967 112.804 103.29 111.841C103.414 111.464 103.475 111.218 103.475 111.218L101.057 108.792C101.697 108.892 102.528 108.654 103.244 108.084C104.615 109.231 108.688 112.735 108.958 112.927C109.012 112.966 109.073 112.866 109.135 112.673C109.405 111.865 109.713 109.501 109.505 109.285C109.312 109.069 106.263 106.505 104.846 105.312C106.032 100.645 102.859 94.4465 97.908 90.1344Z" fill="url(#paint3_linear_11_40)"/>
      <path opacity="0.7" d="M97.9081 90.1343C90.4619 83.6506 86.7812 84.3821 83.2698 85.4063C80.8365 86.0377 79.1425 87.4931 76.1702 84.9058C74.607 83.5428 74.0834 81.895 74.4299 80.8708C74.8072 79.7389 77.1789 79.916 80.929 82.357C81.0137 82.2261 81.1215 82.0028 81.2216 81.687C81.5835 80.6013 81.9916 78.5068 82.0147 76.1043C79.3119 73.7481 69.9407 65.5857 68.7625 64.6617C68.7625 64.6617 66.7864 68.1046 66.4464 68.1346C66.4064 68.1646 69.316 70.2846 70.876 71.4746C69.716 71.1546 67.2164 70.8646 64.5864 71.4046C61.3164 69.2446 56.2964 66.0246 56.2264 66.0746C56.1764 66.1146 54.1164 67.3846 53.0764 68.1446C52.6864 68.4346 52.4364 68.6446 52.4964 68.6846C52.7164 68.8346 56.5864 71.6346 59.7764 73.7446C56.6164 76.6946 59.2764 83.4246 67.3864 89.0646C76.5264 95.4146 83.2964 96.0946 90.5164 96.3446C96.5664 96.5746 101.036 95.6546 103.716 97.5146C106.686 99.5746 105.966 101.065 105.286 101.555C104.116 102.405 101.196 102.195 96.6964 99.3446C96.5864 99.2646 95.7064 99.7946 94.7064 100.535C92.9764 101.795 90.8664 103.665 91.5664 104.155C93.9564 105.815 107.656 116.245 108.006 116.215C110.456 116.025 112.626 114.665 113.646 113.915C114.046 113.625 114.266 113.425 114.266 113.425L110.966 110.755C112.086 110.995 113.706 110.995 115.266 110.685C117.236 112.005 123.086 116.015 123.486 116.235C123.566 116.285 123.726 116.205 123.926 116.055C124.776 115.435 126.406 113.475 126.126 113.245ZM123.556 115.935C123.116 115.655 121.056 114.255 119.226 113.015C117.736 112.005 116.246 110.985 115.426 110.435L115.326 110.365L115.206 110.385C113.756 110.675 112.156 110.695 111.026 110.455L110.776 110.985L113.796 113.425C113.706 113.495 113.596 113.575 113.476 113.665C112.446 114.415 110.386 115.695 108.056 115.895C107.396 115.545 102.426 111.825 98.4064 108.815C95.4764 106.625 92.6964 104.545 91.7464 103.885C91.6464 103.605 92.6864 102.365 94.8864 100.755C95.7764 100.105 96.4064 99.7246 96.6364 99.6346C101.296 102.565 104.246 102.665 105.466 101.775C106.026 101.365 106.686 100.295 105.636 98.8546C105.256 98.3246 104.666 97.7846 103.896 97.2446C101.946 95.8946 99.1264 95.9446 95.5664 96.0246C94.0664 96.0546 92.3564 96.0946 90.5464 96.0246C83.3064 95.7746 76.5864 95.0646 67.5764 88.8046C64.8864 86.9346 62.6464 84.8246 61.1064 82.7046C58.4964 79.1046 58.0664 75.7446 59.9964 73.9446L60.2764 73.6846L59.9564 73.4746C58.1264 72.2646 55.7764 70.6346 52.9564 68.6146L52.9364 68.6046C53.0064 68.5446 53.1164 68.4646 53.2564 68.3646C54.0964 67.7546 55.6064 66.7946 56.1764 66.4446C56.2064 66.4246 56.2264 66.4146 56.2464 66.3946C57.0464 66.8446 61.2664 69.5446 64.4264 71.6346L64.5264 71.7046L64.6464 71.6846C67.3364 71.1346 69.7764 71.4746 70.7964 71.7546L71.0564 71.2246C70.4064 70.7346 69.5264 70.0746 68.7164 69.4746C68.0964 69.0146 67.4164 68.5046 67.0064 68.1946C68.0064 67.6946 70.1664 66.4446 72.0364 65.3346C73.5064 66.2846 79.7464 70.5846 90.6164 78.1346C89.5064 80.0746 87.9264 81.6246 86.8764 82.3946C86.6364 82.5746 86.4064 82.7146 86.2264 82.8046C81.0164 80.0846 76.3664 78.9446 74.8764 80.0246C73.9364 80.7046 73.8564 81.8346 74.6664 82.9446C75.0264 83.4446 75.5464 83.9246 76.2264 84.3946C79.7264 86.8246 82.8464 86.7446 86.4664 86.6546C87.3064 86.6346 88.1864 86.6046 89.0864 86.6246C96.6664 86.6046 103.266 87.1446 113.436 94.2146C115.826 95.8746 117.796 97.7146 119.116 99.5346C121.696 103.085 121.736 106.255 119.226 108.455L118.936 108.715L119.256 108.935C122.866 111.345 125.316 113.005 125.826 113.385C125.646 113.825 124.516 115.235 123.736 115.805C123.656 115.885 123.596 115.915 123.556 115.935ZM101.336 98.0246L104.136 104.045L112.646 105.885L109.866 99.8646L101.336 98.0246ZM112.126 105.465L104.346 103.785L101.866 98.4446L109.656 100.125L112.126 105.465ZM142.556 84.0446L139.766 78.0346L131.246 76.1946L134.036 82.2246L142.556 84.0446ZM131.776 76.6046L139.566 78.2846L142.036 83.6146L134.256 81.9446L131.776 76.6046ZM147.846 72.1346C119.656 52.5546 85.1164 45.0946 70.8364 55.5146C56.5664 65.9346 67.8864 90.3546 96.0664 109.935C124.256 129.515 158.796 136.965 173.066 126.545C187.346 116.125 176.026 91.7246 147.846 72.1346ZM96.2364 109.695C86.5064 102.935 78.4364 95.3346 72.9064 87.7246C62.9964 74.0846 62.2864 62.1346 71.0064 55.7646C85.1864 45.4146 119.576 52.8746 147.666 72.3846C157.396 79.1446 165.466 86.7446 170.996 94.3546C180.906 107.995 181.606 119.935 172.886 126.305C158.716 136.665 124.326 129.205 96.2364 109.695ZM167.006 93.7146C161.946 86.7446 154.566 79.7946 145.666 73.6046C119.946 55.7346 88.3764 48.9546 75.3064 58.5046C67.0864 64.5046 67.6864 75.6646 76.9064 88.3646C81.9664 95.3346 89.3464 102.285 98.2464 108.475C123.966 126.345 155.526 133.115 168.596 123.565C176.816 117.565 176.216 106.405 167.006 93.7146ZM98.4164 108.225C72.7964 90.4246 62.4964 68.2246 75.4764 58.7446C88.4564 49.2746 119.856 56.0546 145.486 73.8546C171.106 91.6546 181.396 113.845 168.416 123.315C155.446 132.795 124.046 126.025 98.4164 108.225Z" fill="url(#paint4_linear_11_40)"/>
      <path d="M107.874 142.189C107.982 142.158 108.082 142.119 108.182 142.081C108.413 142.004 108.637 141.919 108.86 141.834C108.983 141.788 109.099 141.734 109.222 141.68C109.422 141.596 109.622 141.503 109.815 141.411C109.938 141.349 110.053 141.288 110.169 141.226C110.361 141.126 110.546 141.026 110.723 140.91C110.831 140.841 110.947 140.78 111.054 140.71C111.247 140.587 111.432 140.456 111.617 140.325C111.709 140.264 111.801 140.202 111.886 140.133C112.156 139.933 112.417 139.725 112.664 139.501C112.664 139.501 112.664 139.501 112.671 139.501L121.034 132.101C120.78 132.325 120.518 132.533 120.249 132.733C120.164 132.794 120.071 132.856 119.979 132.918C119.794 133.048 119.602 133.179 119.409 133.303C119.301 133.372 119.186 133.433 119.078 133.503C118.893 133.611 118.709 133.718 118.524 133.818C118.408 133.88 118.285 133.942 118.169 134.003C117.977 134.096 117.777 134.188 117.577 134.273C117.461 134.327 117.338 134.373 117.215 134.427C116.991 134.519 116.76 134.596 116.529 134.673C116.429 134.712 116.329 134.75 116.221 134.781C115.883 134.889 115.544 134.989 115.189 135.074C115.151 135.081 115.112 135.089 115.082 135.097C114.774 135.174 114.458 135.235 113.135 135.297C113.75 135.366 114.364 135.42 114.972 135.466C114.933 135.474 114.895 135.482 114.849 135.482C114.41 135.528 113.963 135.559 113.509 135.582C113.37 135.59 113.224 135.59 113.078 135.59C112.762 135.597 112.446 135.605 112.115 135.597C111.938 135.597 111.753 135.582 111.568 135.574C111.268 135.559 110.975 135.551 110.667 135.528C110.452 135.513 110.236 135.489 110.013 135.474C109.789 135.451 109.566 135.428 109.343 135.405C109.112 135.374 108.873 135.343 108.634 135.305C108.403 135.274 108.18 135.235 107.949 135.197C107.71 135.158 107.464 135.112 107.225 135.066C106.994 135.02 106.756 134.974 106.517 134.927C106.27 134.873 106.024 134.82 105.778 134.758C105.539 134.704 105.3 134.642 105.062 134.581C104.8 134.512 104.538 134.442 104.276 134.373C104.053 134.311 103.829 134.25 103.598 134.18C103.329 134.103 103.059 134.019 102.79 133.926C102.559 133.857 102.336 133.78 102.105 133.703C101.827 133.611 101.55 133.51 101.273 133.41C101.042 133.326 100.819 133.249 100.588 133.164C100.303 133.056 100.018 132.941 99.7329 132.833C99.5019 132.74 99.2786 132.656 99.0476 132.563C98.7858 132.456 98.524 132.34 98.2621 132.232C97.9849 132.117 97.7078 131.994 97.4306 131.87C97.1687 131.755 96.9069 131.632 96.6451 131.508C96.3602 131.377 96.083 131.247 95.7981 131.108C95.5363 130.977 95.2667 130.846 94.9972 130.708C94.7123 130.561 94.4274 130.423 94.1425 130.276C93.873 130.138 93.6035 129.991 93.334 129.845C93.0491 129.691 92.7642 129.537 92.4716 129.383C92.2714 129.268 92.0634 129.152 91.8632 129.037C91.4089 128.782 90.9546 128.513 90.5003 128.243C90.277 128.113 90.0537 127.974 89.8304 127.835C89.3761 127.558 88.9217 127.273 88.4674 126.981C88.2595 126.85 88.0593 126.719 87.8514 126.588C87.2123 126.172 86.5654 125.741 85.9186 125.294C85.9032 125.287 85.8955 125.279 85.8801 125.271C85.2256 124.817 84.5634 124.347 83.9011 123.862C83.5931 123.639 83.2851 123.4 82.9694 123.169C82.7307 122.992 82.492 122.815 82.2533 122.63C81.9145 122.368 81.5756 122.106 81.2368 121.837C81.0135 121.66 80.7902 121.49 80.5669 121.306C80.1973 121.005 79.82 120.697 79.4504 120.389C79.2579 120.235 79.0653 120.081 78.8805 119.919C78.3184 119.45 77.7563 118.965 77.1865 118.48C76.4395 117.833 75.7003 117.17 74.9765 116.501C74.7378 116.277 74.4991 116.054 74.2604 115.831C73.7753 115.376 73.2901 114.922 72.8204 114.46C72.5432 114.19 72.2737 113.921 71.9965 113.651C71.5653 113.228 71.1418 112.804 70.7183 112.373C70.4333 112.088 70.1561 111.796 69.8789 111.503C69.4785 111.08 69.0704 110.656 68.6777 110.232C68.4005 109.932 68.1309 109.64 67.8537 109.339C67.461 108.908 67.0683 108.469 66.6833 108.03C66.4215 107.738 66.1674 107.445 65.9133 107.152C65.5129 106.69 65.1278 106.228 64.7351 105.766C64.5041 105.489 64.2654 105.212 64.0421 104.935C63.6032 104.403 63.1797 103.864 62.7561 103.333C62.5867 103.117 62.4173 102.91 62.2479 102.694C61.6627 101.947 61.0929 101.192 60.5307 100.438C60.3921 100.253 60.2613 100.06 60.1227 99.8756C59.7222 99.3212 59.3141 98.7668 58.9291 98.2123C58.7366 97.9351 58.5441 97.6502 58.3516 97.373C58.0282 96.9033 57.7125 96.4413 57.3968 95.9716C57.1889 95.6636 56.9963 95.3556 56.7961 95.0475C56.5112 94.6086 56.2263 94.162 55.9491 93.7231C55.7489 93.3997 55.5487 93.0763 55.3562 92.7528C55.0944 92.3216 54.8326 91.8904 54.5784 91.4669C54.3474 91.0742 54.1165 90.6738 53.8854 90.2811C53.7391 90.027 53.5928 89.7728 53.4542 89.5264C53.1924 89.0567 52.9383 88.5947 52.6919 88.125C52.5841 87.9171 52.4686 87.7169 52.3608 87.509C52.0065 86.839 51.6677 86.1691 51.3366 85.5069C51.2981 85.4299 51.2673 85.3529 51.2288 85.2759C50.9362 84.683 50.659 84.09 50.3818 83.5048C50.2971 83.32 50.2124 83.1275 50.1277 82.9427C49.9198 82.4807 49.7118 82.0187 49.5116 81.5566C49.4192 81.3333 49.3191 81.11 49.2267 80.8944C49.0419 80.4478 48.8649 80.0089 48.6878 79.57C48.603 79.3621 48.526 79.1541 48.4413 78.9539C48.2257 78.3918 48.0178 77.8374 47.8253 77.283C47.8022 77.2137 47.7714 77.1367 47.7483 77.0597C47.525 76.4282 47.3248 75.8045 47.1323 75.1885C47.0707 75.0037 47.0245 74.8189 46.9706 74.6418C46.8397 74.2029 46.7088 73.7639 46.5932 73.3327C46.5316 73.1171 46.4778 72.8938 46.4162 72.6782C46.3084 72.2701 46.2082 71.8697 46.1158 71.4693C46.0696 71.2613 46.0157 71.0457 45.9695 70.8378C45.8848 70.4682 45.8155 70.0986 45.7462 69.7367C45.6923 69.4749 45.6384 69.2131 45.5922 68.9513C45.5229 68.5816 45.469 68.212 45.4151 67.8501C45.3766 67.6114 45.3381 67.365 45.3073 67.1263C45.2534 66.7259 45.2149 66.3332 45.1764 65.9405C45.1533 65.7479 45.1302 65.5477 45.1148 65.3552C45.0686 64.77 45.0301 64.1925 45.0147 63.6304V63.6227C44.9993 63.099 44.9993 62.5908 45.0147 62.0826C45.0147 61.9748 45.0224 61.867 45.0224 61.7592C45.0378 61.2664 45.0686 60.7813 45.1148 60.3038C45.1225 60.2268 45.1302 60.1421 45.1379 60.0651C45.1841 59.6262 45.238 59.2027 45.3073 58.7792C45.3227 58.7022 45.3304 58.6252 45.3458 58.5482C45.4228 58.0939 45.5152 57.6472 45.623 57.2083C45.6461 57.1159 45.6692 57.0158 45.6923 56.9234C45.8078 56.4768 45.931 56.0379 46.0696 55.6067C46.9244 53.0656 48.2488 51.0481 49.966 49.5234L41.6035 56.9234C39.8863 58.4481 38.5618 60.4656 37.7071 63.0066C37.5608 63.4302 37.4376 63.8768 37.3298 64.3234C37.3067 64.4158 37.2836 64.5159 37.2605 64.6083C37.1527 65.0472 37.0603 65.4938 36.9833 65.9558C36.9679 66.0328 36.9602 66.1099 36.9448 66.1869C36.8755 66.6104 36.8216 67.0416 36.7754 67.4728C36.7677 67.5498 36.76 67.6345 36.7523 67.7115C36.7061 68.1889 36.683 68.6741 36.6599 69.1669C36.6599 69.2747 36.6522 69.3825 36.6522 69.4903C36.6368 69.9985 36.6368 70.5067 36.6522 71.0303V71.038C36.6676 71.6079 36.7061 72.1777 36.7523 72.7629C36.7677 72.9554 36.7908 73.1479 36.8139 73.3404C36.8524 73.7408 36.8909 74.1336 36.9448 74.5417C36.9756 74.7804 37.0141 75.0191 37.0526 75.2578C37.1065 75.6274 37.1604 75.997 37.2297 76.3666C37.2759 76.6284 37.3298 76.8903 37.3837 77.1444C37.4376 77.4062 37.4761 77.668 37.5377 77.9298C37.5608 78.0376 37.5916 78.1454 37.6147 78.2532C37.6609 78.4611 37.7071 78.669 37.761 78.8769C37.8534 79.2774 37.9535 79.6855 38.0613 80.0936C38.1152 80.3092 38.1768 80.5248 38.2384 80.7404C38.3616 81.1716 38.4849 81.6105 38.6158 82.0495C38.6697 82.2343 38.7236 82.4191 38.7852 82.6039C38.9777 83.2122 39.1779 83.8205 39.3935 84.4365C39.4012 84.4519 39.4012 84.4596 39.4089 84.475C39.432 84.5443 39.4628 84.6213 39.4859 84.6984C39.6784 85.2528 39.8863 85.8072 40.1019 86.3693C40.1789 86.5772 40.2636 86.7851 40.3483 86.9853C40.5254 87.4243 40.7025 87.8632 40.8874 88.3098C40.9798 88.5331 41.0722 88.7564 41.1723 88.9797C41.3725 89.4417 41.5804 89.9037 41.7883 90.3735C41.873 90.5583 41.95 90.7431 42.0424 90.9279C42.0732 90.9972 42.104 91.0588 42.1271 91.1204C42.3658 91.644 42.6199 92.1599 42.8817 92.6835C42.9202 92.7605 42.9587 92.8452 42.9972 92.9223C43.3283 93.5845 43.6671 94.2544 44.0213 94.9243C44.1291 95.1245 44.2447 95.3324 44.3525 95.5326C44.6066 96.0024 44.8607 96.4644 45.1225 96.9341C45.2611 97.1882 45.4074 97.4423 45.5537 97.6887C45.7077 97.9505 45.8463 98.2124 46.0003 98.4742C46.0773 98.6051 46.162 98.736 46.239 98.8746C46.4931 99.3058 46.7549 99.737 47.0168 100.168C47.217 100.492 47.4095 100.815 47.6097 101.131C47.8869 101.577 48.1718 102.016 48.4644 102.455C48.6646 102.763 48.8571 103.071 49.0651 103.379C49.3808 103.849 49.7042 104.319 50.0199 104.788C50.2124 105.066 50.3972 105.343 50.5897 105.62C50.9824 106.174 51.3828 106.737 51.7909 107.291C51.9141 107.46 52.0373 107.638 52.1605 107.807C52.1682 107.822 52.1837 107.838 52.1914 107.853C52.7535 108.608 53.3233 109.362 53.9085 110.117C54.0702 110.325 54.2397 110.533 54.4091 110.741C54.8326 111.28 55.2638 111.819 55.7027 112.358C55.9337 112.635 56.1647 112.912 56.3957 113.189C56.7807 113.651 57.1734 114.113 57.5739 114.575C57.828 114.868 58.0898 115.168 58.3516 115.461C58.7366 115.9 59.1293 116.339 59.522 116.77C59.7915 117.07 60.0687 117.363 60.3382 117.656C60.7387 118.087 61.1391 118.51 61.5472 118.934C61.8244 119.226 62.1016 119.511 62.3865 119.796C62.8101 120.227 63.2336 120.651 63.6648 121.082C63.9343 121.352 64.2038 121.621 64.481 121.891C64.9584 122.353 65.4435 122.807 65.9287 123.261C66.1674 123.485 66.3984 123.708 66.6371 123.931C67.3609 124.601 68.1002 125.263 68.8471 125.91C69.4092 126.403 69.979 126.881 70.5411 127.35C70.7336 127.512 70.9185 127.658 71.111 127.812C71.4883 128.12 71.8579 128.428 72.2352 128.729C72.4662 128.913 72.6895 129.09 72.9205 129.268C73.2516 129.529 73.5904 129.791 73.9216 130.045C74.1603 130.23 74.4067 130.407 74.6454 130.592C74.8918 130.777 75.1382 130.962 75.3769 131.146C75.4385 131.193 75.5001 131.231 75.5617 131.277C76.2239 131.762 76.8862 132.232 77.5407 132.687C77.5484 132.694 77.5561 132.702 77.5715 132.702C78.2183 133.149 78.8651 133.58 79.512 133.996C79.7199 134.126 79.9201 134.257 80.128 134.388C80.5823 134.681 81.0366 134.966 81.491 135.243C81.7143 135.382 81.9376 135.513 82.1609 135.651C82.6152 135.921 83.0772 136.19 83.5315 136.444C83.6702 136.521 83.8087 136.606 83.9473 136.683C84.0089 136.721 84.0783 136.752 84.1399 136.783C84.4248 136.945 84.7174 137.099 85.0023 137.245C85.2718 137.391 85.5413 137.538 85.8108 137.676C86.0957 137.823 86.3806 137.961 86.6579 138.108C86.9274 138.238 87.1969 138.377 87.4587 138.508C87.7359 138.647 88.0208 138.77 88.298 138.901C88.5675 139.024 88.8293 139.147 89.0988 139.27C89.376 139.393 89.6456 139.509 89.9228 139.624C90.1384 139.717 90.3463 139.809 90.5619 139.902C90.6158 139.925 90.662 139.94 90.7159 139.963C90.9469 140.056 91.1702 140.14 91.4012 140.233C91.6861 140.348 91.9787 140.456 92.2636 140.564C92.4947 140.649 92.7257 140.733 92.9567 140.81C93.2339 140.91 93.5111 141.011 93.7883 141.103C94.0193 141.18 94.2426 141.249 94.4736 141.326C94.7431 141.411 95.0126 141.496 95.2821 141.573C95.5132 141.642 95.7365 141.704 95.9598 141.765C96.1292 141.811 96.2986 141.865 96.4603 141.904C96.5527 141.927 96.6451 141.95 96.7375 141.973C96.9762 142.035 97.2226 142.089 97.4613 142.15C97.7077 142.212 97.9542 142.266 98.2006 142.32C98.4393 142.373 98.6703 142.412 98.909 142.458C99.1554 142.504 99.3941 142.551 99.6328 142.589C99.8638 142.628 100.0949 142.666 100.3182 142.697C100.5569 142.728 100.7879 142.766 101.0266 142.797C101.2499 142.828 101.4732 142.851 101.6965 142.866C101.8274 142.882 101.966 142.897 102.097 142.913C102.182 142.92 102.259 142.92 102.343 142.928C102.644 142.951 102.944 142.966 103.244 142.974C103.429 142.982 103.606 142.99 103.783 142.997C104.107 143.005 104.43 142.997 104.746 142.99C104.884 142.99 105.031 142.99 105.169 142.982C105.624 142.959 106.07 142.928 106.509 142.882C106.548 142.874 106.586 142.866 106.632 142.866C107.025 142.82 107.418 142.766 107.795 142.697C107.826 142.689 107.857 142.689 107.888 142.682C108.18 142.628 108.465 142.566 108.75 142.497C108.788 142.489 108.819 142.481 108.858 142.474C109.197 142.389 109.535 142.296 109.874 142.189Z" fill="url(#paint5_linear_11_40)"/>
    </g>
    <defs>
      <filter id="filter0_d_11_40" x="-15.3594" y="0" width="191.363" height="197" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="4"/>
        <feGaussianBlur stdDeviation="25"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0666666 0 0 0 0 0.0941176 0 0 0 0 0.286275 0 0 0 0.5 0"/>
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11_40"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_11_40" result="shape"/>
      </filter>
      <linearGradient id="paint0_linear_11_40" x1="44.6026" y1="54.5749" x2="122.512" y2="125.321" gradientUnits="userSpaceOnUse">
        <stop offset="0.0317" stopColor="#FCE878"/>
        <stop offset="0.0527" stopColor="#FCE676"/>
        <stop offset="0.6976" stopColor="#FEB432"/>
        <stop offset="1" stopColor="#FFA018"/>
      </linearGradient>
      <linearGradient id="paint1_linear_11_40" x1="108.477" y1="110.975" x2="44.8421" y2="57.4419" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFE6B9" stopOpacity="0"/>
        <stop offset="0.3821" stopColor="#FFF3DE" stopOpacity="0.4239"/>
        <stop offset="0.7037" stopColor="#FFFCF6" stopOpacity="0.7807"/>
        <stop offset="0.9014" stopColor="white"/>
      </linearGradient>
      <linearGradient id="paint2_linear_11_40" x1="46.7103" y1="56.9522" x2="122.757" y2="125.276" gradientUnits="userSpaceOnUse">
        <stop offset="0.0431" stopColor="#FFA018"/>
        <stop offset="0.9874" stopColor="#D36B18"/>
      </linearGradient>
      <linearGradient id="paint3_linear_11_40" x1="138.122" y1="130.241" x2="37.8297" y2="55.0676" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFB817"/>
        <stop offset="0.9893" stopColor="#FFFFBC"/>
      </linearGradient>
      <linearGradient id="paint4_linear_11_40" x1="138.122" y1="130.241" x2="37.8298" y2="55.0675" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFE6B9" stopOpacity="0"/>
        <stop offset="0.3821" stopColor="#FFF3DE" stopOpacity="0.4239"/>
        <stop offset="0.7037" stopColor="#FFFCF6" stopOpacity="0.7807"/>
        <stop offset="0.9014" stopColor="white"/>
      </linearGradient>
      <linearGradient id="paint5_linear_11_40" x1="31.4204" y1="62.3607" x2="111.771" y2="141.134" gradientUnits="userSpaceOnUse">
        <stop offset="0.0394" stopColor="#FFC826"/>
        <stop offset="0.1112" stopColor="#FFA018"/>
        <stop offset="0.4264" stopColor="#F8771E"/>
        <stop offset="0.5129" stopColor="#EC6D1A"/>
        <stop offset="0.6677" stopColor="#CD510E"/>
        <stop offset="0.721" stopColor="#C14609"/>
        <stop offset="0.8348" stopColor="#C3480A"/>
        <stop offset="0.8863" stopColor="#CA4E0D"/>
        <stop offset="0.9252" stopColor="#D75A11"/>
        <stop offset="0.9577" stopColor="#E96A18"/>
        <stop offset="0.977" stopColor="#F8771E"/>
        <stop offset="0.9864" stopColor="#F87A1E"/>
        <stop offset="0.992" stopColor="#FA821C"/>
        <stop offset="0.9967" stopColor="#FC901A"/>
        <stop offset="1" stopColor="#FFA018"/>
      </linearGradient>
      <linearGradient id="paint6_linear_11_40" x1="99.148" y1="121.302" x2="57.6648" y2="38.133" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFE6B9" stopOpacity="0"/>
        <stop offset="1" stopColor="white"/>
      </linearGradient>
    </defs>
  </svg>
);

// Right Coin Component
const CoinRight = () => (
  <svg width="237" height="204" viewBox="0 0 237 204" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g style={{ mixBlendMode: "luminosity" }} filter="url(#filter0_d_11_50)">
      <path d="M151.205 70.0343C119.325 47.8843 80.3152 39.5343 64.0852 51.3943C47.8452 63.2443 60.5352 90.8143 92.4152 112.964C124.295 135.114 163.305 143.464 179.535 131.604C195.765 119.744 183.085 92.1843 151.205 70.0343Z" fill="url(#paint0_linear_11_50)"/>
      <path opacity="0.7" d="M151.205 70.0343C119.325 47.8843 80.3152 39.5343 64.0852 51.3943C47.8452 63.2443 60.5352 90.8143 92.4152 112.964C124.295 135.114 163.305 143.464 179.535 131.604C195.765 119.744 183.085 92.1843 151.205 70.0343ZM92.7252 112.614C81.8052 105.034 72.7652 96.5144 66.5652 87.9743C55.3852 72.5744 54.6252 59.0643 64.5352 51.8243C80.5752 40.1143 119.305 48.4443 150.885 70.3743C161.805 77.9643 170.845 86.4743 177.045 95.0143C188.225 110.414 188.985 123.924 179.075 131.164C163.045 142.874 124.305 134.554 92.7252 112.614Z" fill="url(#paint1_linear_11_50)"/>
      <path d="M133.717 82.6548L130.927 76.6248L139.447 78.4648L142.237 84.4748L133.717 82.6548ZM101.007 98.4748L103.807 104.495L112.317 106.335L109.537 100.315L101.007 98.4748ZM172.737 126.995C158.467 137.415 123.927 129.975 95.7367 110.385C67.5467 90.8048 56.2267 66.3848 70.5067 55.9648C84.7767 45.5448 119.327 52.9948 147.517 72.5848C175.697 92.1648 187.017 116.575 172.737 126.995ZM168.097 123.765C181.077 114.295 170.787 92.1048 145.167 74.3048C119.547 56.5048 88.1367 49.7248 75.1567 59.1948C62.1767 68.6648 72.4767 90.8648 98.0967 108.675C123.717 126.475 155.117 133.245 168.097 123.765ZM157.797 113.695C157.537 113.465 153.147 110.515 151.097 109.145C155.327 105.435 152.347 99.3348 145.277 94.4248C134.647 87.0348 127.607 86.7648 120.767 86.7848C116.057 86.7348 112.307 87.5648 108.077 84.6148C105.847 83.0648 105.637 81.5248 106.727 80.7248C107.927 79.8448 112.157 80.5748 117.887 83.5948C118.107 83.5048 118.397 83.3348 118.727 83.0948C119.877 82.2548 121.567 80.5548 122.687 78.5048C118.827 75.8248 105.447 66.5248 103.717 65.4448C103.717 65.4448 98.4567 68.5548 98.1167 68.5848C98.0767 68.6148 100.987 70.7348 102.547 71.9248C101.387 71.6048 98.8867 71.3148 96.2567 71.8548C92.9867 69.6948 87.9667 66.4748 87.8967 66.5248C87.8467 66.5648 85.7867 67.8348 84.7467 68.5948C84.3567 68.8848 84.1067 69.0948 84.1667 69.1448C84.3867 69.2948 88.2567 72.0948 91.4467 74.2048C88.2867 77.1548 90.9467 83.8948 99.0567 89.5248C108.197 95.8748 114.967 96.5548 122.187 96.8048C128.237 97.0348 132.707 96.1148 135.387 97.9748C138.357 100.035 137.637 101.525 136.957 102.015C135.787 102.865 132.867 102.655 128.367 99.8048C128.257 99.7248 127.377 100.255 126.377 100.995C124.647 102.255 122.537 104.125 123.237 104.615C125.627 106.275 139.327 116.705 139.677 116.675C142.127 116.485 144.297 115.125 145.317 114.375C145.717 114.085 145.937 113.885 145.937 113.885L142.637 111.215C143.757 111.455 145.377 111.455 146.937 111.145C148.907 112.465 154.757 116.475 155.157 116.695C155.237 116.745 155.397 116.665 155.597 116.515C156.447 115.885 158.067 113.925 157.797 113.695Z" fill="url(#paint2_linear_11_50)"/>
      <path d="M134.045 82.2146L131.255 76.1846L139.775 78.0246L142.565 84.0346L134.045 82.2146ZM101.335 98.0246L104.135 104.045L112.645 105.885L109.865 99.8646L101.335 98.0246ZM173.065 126.545C158.795 136.965 124.255 129.525 96.0648 109.935C67.8748 90.3546 56.5548 65.9346 70.8348 55.5146C85.1048 45.0946 119.655 52.5446 147.845 72.1346C176.025 91.7246 187.345 116.125 173.065 126.545ZM168.425 123.325C181.405 113.855 171.115 91.6646 145.495 73.8646C119.875 56.0646 88.4648 49.2846 75.4848 58.7546C62.5048 68.2246 72.8048 90.4246 98.4248 108.235C124.045 126.025 155.445 132.795 168.425 123.325ZM158.125 113.245C157.865 113.015 153.475 110.065 151.425 108.695C155.655 104.985 152.675 98.8846 145.605 93.9746C134.975 86.5846 127.935 86.3146 121.095 86.3346C116.385 86.2846 112.635 87.1146 108.405 84.1646C106.175 82.6146 105.965 81.0746 107.055 80.2746C108.255 79.3946 112.485 80.1246 118.215 83.1446C118.435 83.0546 118.725 82.8846 119.055 82.6446C120.205 81.8046 121.895 80.1046 123.015 78.0546C119.155 75.3746 105.775 66.0746 104.045 64.9946C104.045 64.9946 98.7848 68.1046 98.4448 68.1346C98.4048 68.1646 101.315 70.2846 102.875 71.4746C101.715 71.1546 99.2148 70.8646 96.5848 71.4046C93.3148 69.2446 88.2948 66.0246 88.2248 66.0746C88.1748 66.1146 86.1148 67.3846 85.0748 68.1446C84.6848 68.4346 84.4348 68.6446 84.4948 68.6846C84.7148 68.8346 88.5848 71.6346 91.7748 73.7446C88.6148 76.6946 91.2748 83.4246 99.3848 89.0646C108.525 95.4146 115.295 96.0946 122.515 96.3446C128.565 96.5746 133.035 95.6546 135.715 97.5146C138.685 99.5746 137.965 101.065 137.285 101.555C136.115 102.405 133.195 102.195 128.695 99.3446C128.585 99.2646 127.705 99.7946 126.705 100.535C124.975 101.795 122.865 103.665 123.565 104.155C125.955 105.815 139.655 116.245 140.005 116.215C142.455 116.025 144.625 114.665 145.645 113.915C146.045 113.625 146.265 113.425 146.265 113.425L142.965 110.755C144.085 110.995 145.705 110.995 147.265 110.685C149.235 112.005 155.085 116.015 155.485 116.235C155.565 116.285 155.725 116.205 155.925 116.055C156.775 115.435 158.405 113.475 158.125 113.245Z" fill="url(#paint3_linear_11_50)"/>
      <path opacity="0.7" d="M158.126 113.245C157.866 113.015 153.476 110.065 151.426 108.695C155.656 104.985 152.676 98.8846 145.606 93.9746C134.976 86.5846 127.936 86.3146 121.096 86.3346C116.386 86.2846 112.636 87.1146 108.406 84.1646C106.176 82.6146 105.966 81.0746 107.056 80.2746C108.256 79.3946 112.486 80.1246 118.216 83.1446C118.436 83.0546 118.726 82.8846 119.056 82.6446C120.206 81.8046 121.896 80.1046 123.016 78.0546C119.156 75.3746 105.776 66.0746 104.046 64.9946C104.046 64.9946 98.7864 68.1046 98.4464 68.1346C98.4064 68.1646 101.316 70.2846 102.876 71.4746C101.716 71.1546 99.2164 70.8646 96.5864 71.4046C93.3164 69.2446 88.2964 66.0246 88.2264 66.0746C88.1764 66.1146 86.1164 67.3846 85.0764 68.1446C84.6864 68.4346 84.4364 68.6446 84.4964 68.6846C84.7164 68.8346 88.5864 71.6346 91.7764 73.7446C88.6164 76.6946 91.2764 83.4246 99.3864 89.0646C108.526 95.4146 115.296 96.0946 122.516 96.3446C128.566 96.5746 133.036 95.6546 135.716 97.5146C138.686 99.5746 137.966 101.065 137.286 101.555C136.116 102.405 133.196 102.195 128.696 99.3446C128.586 99.2646 127.706 99.7946 126.706 100.535C124.976 101.795 122.866 103.665 123.566 104.155C125.956 105.815 139.656 116.245 140.006 116.215C142.456 116.025 144.626 114.665 145.646 113.915C146.046 113.625 146.266 113.425 146.266 113.425L142.966 110.755C144.086 110.995 145.706 110.995 147.266 110.685C149.236 112.005 155.086 116.015 155.486 116.235C155.566 116.285 155.726 116.205 155.926 116.055C156.776 115.435 158.406 113.475 158.126 113.245ZM155.556 115.935C155.116 115.655 153.056 114.255 151.226 113.015C149.736 112.005 148.246 110.985 147.426 110.435L147.326 110.365L147.206 110.385C145.756 110.675 144.156 110.695 143.026 110.455L142.776 110.985L145.796 113.425C145.706 113.495 145.596 113.575 145.476 113.665C144.446 114.415 142.386 115.695 140.056 115.895C139.396 115.545 134.426 111.825 130.406 108.815C127.476 106.625 124.696 104.545 123.746 103.885C123.646 103.605 124.686 102.365 126.886 100.755C127.776 100.105 128.406 99.7246 128.636 99.6346C133.296 102.565 136.246 102.665 137.466 101.775C138.026 101.365 138.686 100.295 137.636 98.8546C137.256 98.3246 136.666 97.7846 135.896 97.2446C133.946 95.8946 131.126 95.9446 127.566 96.0246C126.066 96.0546 124.356 96.0946 122.546 96.0246C115.306 95.7746 108.586 95.0646 99.5764 88.8046C96.8864 86.9346 94.6464 84.8246 93.1064 82.7046C90.4964 79.1046 90.0664 75.7446 91.9964 73.9446L92.2764 73.6846L91.9564 73.4746C90.1264 72.2646 87.7764 70.6346 84.9564 68.6146L84.9364 68.6046C85.0064 68.5446 85.1164 68.4646 85.2564 68.3646C86.0964 67.7546 87.6064 66.7946 88.1764 66.4446C88.2064 66.4246 88.2264 66.4146 88.2464 66.3946C89.0464 66.8446 93.2664 69.5446 96.4264 71.6346L96.5264 71.7046L96.6464 71.6846C99.3364 71.1346 101.776 71.4746 102.796 71.7546L103.056 71.2246C102.406 70.7346 101.526 70.0746 100.716 69.4746C100.096 69.0146 99.4164 68.5046 99.0064 68.1946C100.006 67.6946 102.166 66.4446 104.036 65.3346C105.506 66.2846 111.746 70.5846 122.616 78.1346C121.506 80.0746 119.926 81.6246 118.876 82.3946C118.636 82.5746 118.406 82.7146 118.226 82.8046C113.016 80.0846 108.366 78.9446 106.876 80.0246C105.936 80.7046 105.856 81.8346 106.666 82.9446C107.026 83.4446 107.546 83.9246 108.226 84.3946C111.726 86.8246 114.846 86.7446 118.466 86.6546C119.306 86.6346 120.186 86.6046 121.086 86.6246C128.666 86.6046 135.266 87.1446 145.436 94.2146C147.826 95.8746 149.796 97.7146 151.116 99.5346C153.696 103.085 153.736 106.255 151.226 108.455L150.936 108.715L151.256 108.935C154.866 111.345 157.316 113.005 157.826 113.385C157.646 113.825 156.516 115.235 155.736 115.805C155.656 115.885 155.596 115.915 155.556 115.935ZM101.336 98.0246L104.136 104.045L112.646 105.885L109.866 99.8646L101.336 98.0246ZM112.126 105.465L104.346 103.785L101.866 98.4446L109.656 100.125L112.126 105.465ZM142.556 84.0446L139.766 78.0346L131.246 76.1946L134.036 82.2246L142.556 84.0446ZM131.776 76.6046L139.566 78.2846L142.036 83.6146L134.256 81.9446L131.776 76.6046ZM147.846 72.1346C119.656 52.5546 85.1164 45.0946 70.8364 55.5146C56.5664 65.9346 67.8864 90.3546 96.0664 109.935C124.256 129.515 158.796 136.965 173.066 126.545C187.346 116.125 176.026 91.7246 147.846 72.1346ZM96.2364 109.695C86.5064 102.935 78.4364 95.3346 72.9064 87.7246C62.9964 74.0846 62.2864 62.1346 71.0064 55.7646C85.1864 45.4146 119.576 52.8746 147.666 72.3846C157.396 79.1446 165.466 86.7446 170.996 94.3546C180.906 107.995 181.606 119.935 172.886 126.305C158.716 136.665 124.326 129.205 96.2364 109.695ZM167.006 93.7146C161.946 86.7446 154.566 79.7946 145.666 73.6046C119.946 55.7346 88.3764 48.9546 75.3064 58.5046C67.0864 64.5046 67.6864 75.6646 76.9064 88.3646C81.9664 95.3346 89.3464 102.285 98.2464 108.475C123.966 126.345 155.526 133.115 168.596 123.565C176.816 117.565 176.216 106.405 167.006 93.7146ZM98.4164 108.225C72.7964 90.4246 62.4964 68.2246 75.4764 58.7446C88.4564 49.2746 119.856 56.0546 145.486 73.8546C171.106 91.6546 181.396 113.845 168.416 123.315C155.446 132.795 124.046 126.025 98.4164 108.225Z" fill="url(#paint4_linear_11_50)"/>
      <path d="M170.874 145.424C170.884 145.414 170.894 145.414 170.904 145.404C171.464 145.064 172.004 144.714 172.524 144.334C172.564 144.304 172.594 144.284 172.634 144.254C172.864 144.084 173.064 143.894 173.284 143.724C173.564 143.494 173.854 143.274 174.114 143.034C174.134 143.014 174.154 143.004 174.174 142.984C174.364 142.804 174.534 142.614 174.714 142.434C174.964 142.184 175.224 141.934 175.464 141.674C175.484 141.654 175.514 141.624 175.534 141.604C175.694 141.424 175.824 141.224 175.974 141.044C176.194 140.764 176.424 140.494 176.624 140.204C176.644 140.174 176.674 140.144 176.704 140.104C177.004 139.674 177.274 139.224 177.524 138.774L184.544 126.044C184.274 126.534 183.974 127.004 183.644 127.474C183.444 127.764 183.214 128.034 182.994 128.314C182.824 128.524 182.674 128.744 182.484 128.944C182.254 129.204 181.994 129.454 181.734 129.704C181.534 129.904 181.344 130.114 181.134 130.304C180.874 130.544 180.584 130.764 180.304 130.994C180.054 131.194 179.814 131.414 179.544 131.614C179.024 131.994 178.484 132.344 177.924 132.684C177.824 132.744 177.714 132.794 177.614 132.854C177.114 133.144 176.604 133.424 176.064 133.684C175.564 133.924 175.054 134.154 174.534 134.364C174.424 134.404 174.324 134.444 174.214 134.494C173.644 134.714 173.064 134.934 172.464 135.124C172.434 135.134 172.394 135.144 172.364 135.154C171.804 135.334 171.234 135.494 170.644 135.644C170.474 135.684 170.304 135.734 170.134 135.774C169.504 135.924 168.864 136.074 168.204 136.204C168.194 136.204 168.184 136.204 168.174 136.204C167.504 136.334 166.824 136.434 166.124 136.534C165.904 136.564 165.674 136.594 165.454 136.614C164.984 136.674 164.514 136.724 164.034 136.764C163.654 136.804 163.274 136.824 162.884 136.854C162.464 136.884 162.044 136.914 161.624 136.934C161.234 136.954 160.834 136.964 160.434 136.974C159.964 136.984 159.484 136.994 159.004 136.994C158.734 136.994 158.474 136.994 158.204 136.994C157.604 136.984 157.004 136.974 156.394 136.944C156.194 136.934 155.994 136.934 155.794 136.924C154.984 136.884 154.174 136.834 153.344 136.764C153.154 136.744 152.974 136.724 152.784 136.714C152.184 136.664 151.574 136.604 150.964 136.534C149.864 136.414 148.754 136.274 147.624 136.104C147.574 136.094 147.524 136.084 147.474 136.084C146.394 135.914 145.294 135.724 144.184 135.514C143.834 135.444 143.484 135.374 143.124 135.304C142.274 135.134 141.414 134.954 140.554 134.754C140.194 134.674 139.834 134.594 139.474 134.504C138.374 134.244 137.274 133.964 136.164 133.664C136.074 133.644 135.984 133.614 135.894 133.594C134.564 133.234 133.214 132.834 131.864 132.414C131.524 132.304 131.174 132.194 130.834 132.084C129.584 131.684 128.324 131.264 127.054 130.814C126.934 130.774 126.804 130.734 126.684 130.684C126.444 130.594 126.204 130.504 125.964 130.414C125.224 130.144 124.484 129.874 123.734 129.584C123.374 129.444 123.014 129.294 122.644 129.154C122.064 128.924 121.494 128.694 120.914 128.464C119.574 127.914 118.244 127.354 116.904 126.754C116.614 126.624 116.314 126.484 116.024 126.354C115.104 125.934 114.174 125.504 113.254 125.064C112.824 124.864 112.394 124.654 111.974 124.444C111.244 124.084 110.504 123.714 109.774 123.344C109.334 123.114 108.894 122.894 108.454 122.664C107.754 122.294 107.054 121.924 106.354 121.544C105.924 121.314 105.504 121.084 105.074 120.844C104.364 120.444 103.654 120.034 102.934 119.624C102.544 119.404 102.164 119.184 101.774 118.954C100.994 118.494 100.214 118.014 99.4339 117.534C99.1439 117.354 98.8439 117.184 98.5539 117.004C97.4839 116.344 96.4239 115.664 95.3739 114.964C94.3839 114.314 93.3939 113.644 92.4139 112.964C92.1139 112.754 91.8139 112.534 91.5139 112.324C90.7539 111.784 89.9939 111.244 89.2539 110.704C88.9439 110.474 88.6339 110.244 88.3239 110.014C87.6039 109.474 86.8939 108.934 86.1939 108.384C85.9339 108.184 85.6639 107.974 85.4039 107.774C84.4439 107.014 83.5139 106.254 82.5939 105.494C82.4539 105.374 82.3139 105.254 82.1739 105.134C81.3439 104.424 80.5239 103.714 79.7239 103.004C79.4939 102.804 79.2639 102.594 79.0439 102.394C78.0539 101.494 77.0939 100.594 76.1639 99.6944C75.8139 99.3544 75.4739 99.0144 75.1339 98.6744C74.8839 98.4244 74.6239 98.1644 74.3739 97.9144C73.9939 97.5244 73.6239 97.1344 73.2539 96.7544C73.0339 96.5244 72.8139 96.2944 72.5939 96.0644C72.4439 95.9044 72.3039 95.7444 72.1539 95.5844C71.6339 95.0244 71.1339 94.4644 70.6439 93.9044C70.4239 93.6544 70.2139 93.4044 70.0039 93.1644C69.5939 92.6844 69.1939 92.2144 68.8039 91.7344C68.5939 91.4744 68.3839 91.2244 68.1839 90.9744C67.7539 90.4344 67.3239 89.8844 66.9139 89.3444C66.7639 89.1444 66.6039 88.9444 66.4539 88.7444C65.9139 88.0144 65.3839 87.2844 64.8939 86.5544C64.8039 86.4244 64.7139 86.2844 64.6239 86.1544C64.2239 85.5544 63.8339 84.9644 63.4639 84.3644C63.3139 84.1244 63.1739 83.8944 63.0339 83.6544C62.7239 83.1444 62.4239 82.6444 62.1439 82.1344C62.0139 81.9044 61.8739 81.6644 61.7539 81.4344C61.4239 80.8344 61.1139 80.2444 60.8239 79.6544C60.7639 79.5344 60.7039 79.4144 60.6439 79.3044C60.3039 78.6044 59.9839 77.9044 59.6839 77.2044C59.6039 77.0144 59.5339 76.8244 59.4539 76.6344C59.2439 76.1244 59.0539 75.6244 58.8639 75.1144C58.7839 74.8844 58.7039 74.6544 58.6239 74.4244C58.4539 73.9344 58.3039 73.4444 58.1639 72.9644C58.1039 72.7644 58.0439 72.5744 57.9939 72.3744C57.8139 71.7144 57.6539 71.0544 57.5239 70.4044C57.5139 70.3344 57.5039 70.2644 57.4839 70.1844C57.3739 69.6044 57.2839 69.0344 57.2039 68.4744C57.1739 68.2744 57.1639 68.0744 57.1439 67.8744C57.0939 67.4344 57.0639 66.9844 57.0439 66.5544C57.0339 66.3544 57.0239 66.1444 57.0239 65.9444C57.0139 65.4644 57.0239 64.9844 57.0439 64.5144C57.0539 64.3844 57.0439 64.2544 57.0539 64.1244C57.0939 63.5344 57.1639 62.9544 57.2539 62.3844C57.2739 62.2444 57.3139 62.1044 57.3339 61.9544C57.4139 61.5244 57.5039 61.1044 57.6139 60.6844C57.6639 60.5044 57.7139 60.3144 57.7739 60.1344C57.8939 59.7444 58.0239 59.3744 58.1639 58.9944C58.2239 58.8344 58.2839 58.6644 58.3539 58.5144C58.5739 57.9944 58.8039 57.4844 59.0839 56.9944L52.0639 69.7244C51.7939 70.2144 51.5539 70.7244 51.3339 71.2444C51.2639 71.4044 51.2139 71.5644 51.1439 71.7244C51.0039 72.0944 50.8739 72.4744 50.7539 72.8644C50.7039 73.0444 50.6439 73.2244 50.5939 73.4144C50.4839 73.8344 50.3939 74.2544 50.3139 74.6844C50.2839 74.8244 50.2539 74.9644 50.2339 75.1144C50.1439 75.6844 50.0739 76.2644 50.0339 76.8544C50.0239 76.9844 50.0239 77.1144 50.0239 77.2444C50.0039 77.7144 49.9939 78.1844 50.0039 78.6744C50.0039 78.8744 50.0139 79.0844 50.0239 79.2844C50.0439 79.7244 50.0739 80.1644 50.1239 80.6044C50.1439 80.8044 50.1639 81.0044 50.1839 81.2044C50.2539 81.7644 50.3439 82.3344 50.4539 82.9144C50.4639 82.9844 50.4739 83.0644 50.4939 83.1344C50.6239 83.7844 50.7839 84.4444 50.9639 85.1044C51.0139 85.2944 51.0739 85.4944 51.1339 85.6944C51.2739 86.1744 51.4239 86.6644 51.5939 87.1544C51.6739 87.3844 51.7539 87.6144 51.8339 87.8444C52.0139 88.3444 52.2139 88.8544 52.4239 89.3544C52.5039 89.5444 52.5739 89.7344 52.6539 89.9344C52.9539 90.6244 53.2639 91.3244 53.6139 92.0344C53.6739 92.1544 53.7339 92.2744 53.7939 92.3844C54.0839 92.9744 54.3939 93.5744 54.7239 94.1644C54.8539 94.3944 54.9839 94.6244 55.1139 94.8644C55.4039 95.3644 55.6939 95.8744 56.0039 96.3844C56.1439 96.6244 56.2939 96.8544 56.4339 97.0944C56.8039 97.6944 57.1939 98.2844 57.6039 98.8844C57.6939 99.0144 57.7739 99.1444 57.8639 99.2844C58.3639 100.014 58.8839 100.744 59.4339 101.474C59.5839 101.674 59.7339 101.874 59.8839 102.064C60.2939 102.614 60.7239 103.154 61.1539 103.704C61.2439 103.824 61.3339 103.944 61.4339 104.054C61.5439 104.194 61.6639 104.324 61.7739 104.464C62.1639 104.944 62.5639 105.424 62.9739 105.894C63.1839 106.144 63.3939 106.384 63.6139 106.634C64.1039 107.194 64.6139 107.754 65.1239 108.314C65.2439 108.444 65.3539 108.574 65.4739 108.704C65.5039 108.734 65.5339 108.764 65.5539 108.794C65.7639 109.024 65.9939 109.254 66.2139 109.484C66.5839 109.874 66.9439 110.264 67.3239 110.644C67.5739 110.904 67.8339 111.154 68.0939 111.414C68.3839 111.704 68.6739 112.004 68.9739 112.294C69.0239 112.344 69.0739 112.384 69.1239 112.434C70.0539 113.334 71.0139 114.234 72.0039 115.134C72.1039 115.224 72.1839 115.314 72.2839 115.394C72.4139 115.514 72.5539 115.624 72.6839 115.744C73.4839 116.454 74.2939 117.164 75.1339 117.874C75.2439 117.964 75.3339 118.054 75.4439 118.144C75.4739 118.174 75.5139 118.204 75.5439 118.224C76.4539 118.994 77.3939 119.754 78.3539 120.504C78.4339 120.574 78.5139 120.634 78.5939 120.704C78.7739 120.844 78.9639 120.984 79.1339 121.124C79.8339 121.674 80.5439 122.214 81.2639 122.754C81.4339 122.884 81.6039 123.014 81.7739 123.144C81.9139 123.244 82.0539 123.344 82.1939 123.444C82.9339 123.994 83.6939 124.534 84.4639 125.074C84.6439 125.204 84.8239 125.334 85.0039 125.464C85.1239 125.544 85.2339 125.624 85.3539 125.714C86.3139 126.384 87.2839 127.044 88.2639 127.684C88.2839 127.694 88.2939 127.704 88.3139 127.714C89.3639 128.414 90.4239 129.084 91.4939 129.754C91.5239 129.774 91.5439 129.784 91.5739 129.804C91.8439 129.974 92.1039 130.124 92.3739 130.284C93.1539 130.764 93.9339 131.244 94.7139 131.704C94.7839 131.744 94.8439 131.784 94.9139 131.824C95.2339 132.014 95.5539 132.184 95.8739 132.364C96.5839 132.774 97.2939 133.184 98.0139 133.584C98.1339 133.654 98.2539 133.724 98.3739 133.784C98.6839 133.954 98.9839 134.114 99.2939 134.274C99.9939 134.654 100.694 135.034 101.394 135.404C101.574 135.504 101.754 135.604 101.934 135.694C102.194 135.824 102.454 135.954 102.714 136.084C103.444 136.454 104.184 136.824 104.914 137.184C105.164 137.304 105.404 137.434 105.654 137.554C105.834 137.644 106.014 137.724 106.194 137.804C107.114 138.244 108.044 138.674 108.964 139.094C109.194 139.194 109.424 139.314 109.664 139.414C109.724 139.444 109.794 139.464 109.854 139.494C111.194 140.094 112.524 140.654 113.864 141.204C113.984 141.254 114.094 141.304 114.214 141.354C114.674 141.544 115.144 141.714 115.604 141.904C115.964 142.044 116.324 142.194 116.684 142.324C117.434 142.614 118.174 142.884 118.914 143.154C119.154 143.244 119.394 143.334 119.634 143.424C119.664 143.434 119.694 143.444 119.724 143.454C119.814 143.484 119.904 143.514 120.004 143.544C121.274 143.994 122.534 144.414 123.784 144.814C124.124 144.924 124.474 145.034 124.814 145.144C126.164 145.564 127.514 145.964 128.844 146.324C128.854 146.324 128.864 146.334 128.884 146.334C128.964 146.354 129.034 146.374 129.114 146.394C130.224 146.694 131.334 146.974 132.424 147.234C132.784 147.324 133.144 147.404 133.504 147.484C134.364 147.684 135.224 147.864 136.074 148.034C136.424 148.104 136.774 148.174 137.124 148.244C138.234 148.454 139.334 148.644 140.414 148.814C140.464 148.824 140.514 148.834 140.564 148.834C141.694 149.004 142.804 149.144 143.904 149.264C144.014 149.274 144.134 149.294 144.254 149.304C144.754 149.354 145.234 149.394 145.724 149.444C145.914 149.464 146.104 149.484 146.284 149.494C147.114 149.564 147.924 149.614 148.734 149.654C148.934 149.664 149.134 149.664 149.334 149.674C149.944 149.694 150.544 149.714 151.144 149.724C151.414 149.724 151.674 149.724 151.944 149.724C152.194 149.724 152.444 149.734 152.694 149.724C152.924 149.724 153.154 149.704 153.384 149.704C153.784 149.694 154.174 149.684 154.574 149.664C155.004 149.644 155.414 149.614 155.834 149.584C156.224 149.554 156.604 149.524 156.984 149.494C157.154 149.474 157.334 149.474 157.504 149.454C157.814 149.424 158.104 149.374 158.414 149.344C158.634 149.314 158.864 149.294 159.084 149.264C159.774 149.164 160.454 149.064 161.124 148.934C161.134 148.934 161.144 148.934 161.154 148.924C161.814 148.794 162.454 148.654 163.084 148.504C163.254 148.464 163.424 148.414 163.584 148.374C163.774 148.324 163.964 148.284 164.144 148.234C164.534 148.124 164.924 148.004 165.304 147.884C165.334 147.874 165.364 147.864 165.394 147.854C165.824 147.714 166.254 147.564 166.674 147.414C166.834 147.354 166.994 147.284 167.154 147.224C167.264 147.184 167.364 147.144 167.474 147.094C167.954 146.904 168.414 146.704 168.874 146.484C168.924 146.464 168.964 146.434 169.004 146.414C169.534 146.154 170.054 145.874 170.554 145.584C170.684 145.514 170.784 145.474 170.874 145.424Z" fill="url(#paint5_linear_11_50)"/>
      <path opacity="0.7" d="M166.703 136.424L68.5929 91.434C66.1629 88.434 64.0829 85.454 62.3929 82.534L174.963 134.154C172.503 135.204 169.733 135.954 166.703 136.424ZM186.413 120.264L57.4929 61.144C57.0629 62.984 56.9429 64.954 57.0829 67.014L184.753 125.554C185.593 123.924 186.143 122.144 186.413 120.264ZM182.063 129.364C182.653 128.744 183.173 128.084 183.643 127.404L57.3829 69.504C57.5529 70.484 57.7629 71.474 58.0429 72.494L182.063 129.364ZM173.853 90.164L78.3529 46.374C72.6529 47.054 67.7729 48.694 64.0729 51.394C63.9329 51.494 63.8029 51.614 63.6729 51.724L184.423 107.094C182.333 101.694 178.763 95.954 173.853 90.164Z" fill="url(#paint6_linear_11_50)"/>
    </g>
    <defs>
      <filter id="filter0_d_11_50" x="0" y="0" width="236.6" height="203.729" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="4"/>
        <feGaussianBlur stdDeviation="25"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0666666 0 0 0 0 0.0941176 0 0 0 0 0.286275 0 0 0 0.5 0"/>
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11_50"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_11_50" result="shape"/>
      </filter>
      <linearGradient id="paint0_linear_11_50" x1="170.587" y1="78.234" x2="71.903" y2="105.066" gradientUnits="userSpaceOnUse">
        <stop offset="0.0317" stopColor="#FCE878"/>
        <stop offset="0.0527" stopColor="#FCE676"/>
        <stop offset="0.6976" stopColor="#FEB432"/>
        <stop offset="1" stopColor="#FFA018"/>
      </linearGradient>
      <linearGradient id="paint1_linear_11_50" x1="43.5505" y1="93.1455" x2="177.153" y2="90.3303" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFE6B9" stopOpacity="0"/>
        <stop offset="0.3821" stopColor="#FFF3DE" stopOpacity="0.4239"/>
        <stop offset="0.7037" stopColor="#FFFCF6" stopOpacity="0.7807"/>
        <stop offset="0.9014" stopColor="white"/>
      </linearGradient>
      <linearGradient id="paint2_linear_11_50" x1="163.231" y1="87.6634" x2="72.1949" y2="96.016" gradientUnits="userSpaceOnUse">
        <stop offset="0.0431" stopColor="#FFA018"/>
        <stop offset="0.9874" stopColor="#D36B18"/>
      </linearGradient>
      <linearGradient id="paint3_linear_11_50" x1="52.485" y1="127.964" x2="152.364" y2="74.8656" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFB817"/>
        <stop offset="0.9893" stopColor="#FFFFBC"/>
      </linearGradient>
      <linearGradient id="paint4_linear_11_50" x1="79.0839" y1="92.3664" x2="175.365" y2="89.3771" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFE6B9" stopOpacity="0"/>
        <stop offset="0.3821" stopColor="#FFF3DE" stopOpacity="0.4239"/>
        <stop offset="0.7037" stopColor="#FFFCF6" stopOpacity="0.7807"/>
        <stop offset="0.9014" stopColor="white"/>
      </linearGradient>
      <linearGradient id="paint5_linear_11_50" x1="173.38" y1="146.166" x2="47.5076" y2="78.6023" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFC826"/>
        <stop offset="0.0945" stopColor="#FFA018"/>
        <stop offset="0.2186" stopColor="#F8771E"/>
        <stop offset="0.3662" stopColor="#EC6D1A"/>
        <stop offset="0.6301" stopColor="#CD510E"/>
        <stop offset="0.721" stopColor="#C14609"/>
        <stop offset="0.7796" stopColor="#C64A0B"/>
        <stop offset="0.8451" stopColor="#D35610"/>
        <stop offset="0.9137" stopColor="#E96A18"/>
        <stop offset="0.9495" stopColor="#F8771E"/>
        <stop offset="0.9701" stopColor="#F87A1E"/>
        <stop offset="0.9825" stopColor="#FA821C"/>
        <stop offset="0.9927" stopColor="#FC901A"/>
        <stop offset="1" stopColor="#FFA018"/>
      </linearGradient>
      <linearGradient id="paint6_linear_11_50" x1="67.1031" y1="92.5345" x2="199.69" y2="89.8133" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFE6B9" stopOpacity="0"/>
        <stop offset="0.9014" stopColor="white"/>
      </linearGradient>
    </defs>
  </svg>
);

// Star Component
const Star = () => (
  <svg width="67" height="71" viewBox="0 0 67 71" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M46.566 5.7554C46.566 5.7454 46.566 5.7354 46.556 5.7354C46.546 5.7054 46.5359 5.6754 46.5259 5.6454C46.5159 5.6154 46.506 5.58539 46.496 5.55539C46.486 5.52539 46.4659 5.5054 46.4559 5.4754C46.4559 5.4754 46.4559 5.47539 46.4559 5.46539C46.4559 5.46539 46.4559 5.4654 46.4559 5.4554C46.4459 5.4354 46.436 5.4154 46.416 5.3954C46.396 5.3754 46.386 5.34539 46.366 5.32539C46.346 5.30539 46.326 5.2854 46.306 5.2654C46.296 5.2554 46.2859 5.2454 46.2759 5.2354L41.0859 0.275391C41.1559 0.335391 41.206 0.415392 41.256 0.495392C41.256 0.495392 41.256 0.495402 41.256 0.505402C41.306 0.595402 41.346 0.685395 41.366 0.795395L44.796 14.6854L49.986 19.6454L46.566 5.7554Z" fill="url(#paint0_linear_11_58)"/>
    <path d="M21.3703 53.4854L2.67029 64.8454C2.49029 64.9554 2.32027 65.0454 2.15027 65.1154C2.14027 65.1154 2.14031 65.1254 2.13031 65.1254C1.97031 65.1954 1.81028 65.2454 1.66028 65.2854C1.08028 65.4254 0.620313 65.3354 0.320312 65.0554L5.51031 70.0154C5.80031 70.2954 6.27028 70.3854 6.85028 70.2454C6.94028 70.2254 7.03031 70.1954 7.13031 70.1654C7.19031 70.1454 7.26026 70.1154 7.33026 70.0854C7.34026 70.0854 7.34028 70.0754 7.35028 70.0754C7.36028 70.0754 7.3603 70.0754 7.3703 70.0654C7.4303 70.0354 7.5003 70.0054 7.5603 69.9754C7.6203 69.9454 7.67029 69.9154 7.73029 69.8854C7.78029 69.8554 7.83031 69.8254 7.88031 69.7954L26.5803 58.4354L21.3703 53.4854Z" fill="url(#paint1_linear_11_58)"/>
    <path d="M66.9088 20.875C66.9088 20.845 66.9088 20.815 66.9188 20.785C66.9188 20.735 66.9188 20.695 66.9188 20.655C66.9188 20.615 66.9188 20.575 66.9088 20.535C66.9088 20.515 66.8987 20.495 66.8987 20.485C66.8987 20.475 66.8987 20.475 66.8987 20.465C66.8987 20.455 66.8987 20.435 66.8887 20.425C66.8787 20.395 66.8788 20.355 66.8688 20.325C66.8588 20.295 66.8487 20.265 66.8387 20.235C66.8287 20.205 66.8188 20.175 66.8088 20.155C66.8088 20.145 66.7988 20.135 66.7988 20.125V20.115C66.7888 20.105 66.7887 20.085 66.7787 20.075C66.7687 20.045 66.7488 20.025 66.7388 20.005C66.7188 19.985 66.7088 19.955 66.6888 19.935C66.6688 19.915 66.6488 19.895 66.6288 19.865C66.6188 19.855 66.6088 19.845 66.5988 19.835L61.4088 14.875C61.4888 14.955 61.5588 15.045 61.6088 15.155V15.165C61.6588 15.265 61.6887 15.385 61.7087 15.505C61.7087 15.515 61.7087 15.515 61.7087 15.525C61.7287 15.645 61.7288 15.775 61.7188 15.915C61.7188 15.925 61.7188 15.935 61.7188 15.945C61.7087 16.075 61.6787 16.215 61.6487 16.355C61.6387 16.375 61.6387 16.395 61.6387 16.415C61.6187 16.495 61.5788 16.585 61.5488 16.675C61.5188 16.755 61.4987 16.835 61.4587 16.925C61.4187 17.005 61.3687 17.095 61.3287 17.185C61.2887 17.275 61.2488 17.355 61.1888 17.445C61.0888 17.605 60.9788 17.765 60.8588 17.925C60.8588 17.935 60.8488 17.935 60.8488 17.945C60.7188 18.115 60.5688 18.285 60.4088 18.455L43.9688 35.355L49.1588 40.315L65.5988 23.415C65.6088 23.405 65.6088 23.405 65.6188 23.395C65.6588 23.355 65.6988 23.315 65.7388 23.275C65.7788 23.235 65.8088 23.195 65.8488 23.155C65.8888 23.105 65.9287 23.065 65.9688 23.015C65.9988 22.985 66.0188 22.955 66.0488 22.915C66.0488 22.905 66.0588 22.905 66.0588 22.895C66.0688 22.885 66.0787 22.875 66.0787 22.865C66.1187 22.805 66.1687 22.745 66.2087 22.685C66.2687 22.605 66.3188 22.525 66.3688 22.445C66.3788 22.435 66.3787 22.425 66.3887 22.415C66.4387 22.325 66.4787 22.245 66.5287 22.165C66.5787 22.075 66.6288 21.995 66.6588 21.905C66.6988 21.825 66.7188 21.745 66.7488 21.655C66.7788 21.585 66.8087 21.505 66.8287 21.435C66.8287 21.425 66.8387 21.405 66.8387 21.395C66.8487 21.375 66.8488 21.355 66.8488 21.335C66.8588 21.275 66.8787 21.225 66.8887 21.165C66.8987 21.095 66.9088 21.035 66.9188 20.975C66.9188 20.955 66.9188 20.945 66.9188 20.925C66.9088 20.885 66.9088 20.875 66.9088 20.875Z" fill="url(#paint2_linear_11_58)"/>
    <path d="M43.955 35.3252L39.175 53.5552C39.135 53.7052 39.075 53.8452 39.015 53.9852C38.995 54.0352 38.985 54.0852 38.965 54.1352C38.885 54.3152 38.785 54.4952 38.675 54.6752L38.665 54.6852C38.555 54.8652 38.435 55.0352 38.295 55.1952C38.155 55.3652 38.015 55.5252 37.865 55.6752C37.715 55.8252 37.5549 55.9652 37.3849 56.0952C37.2249 56.2252 37.0549 56.3452 36.8849 56.4452C36.8849 36.8859 36.885 56.4452 36.875 56.4452C36.705 56.5452 36.535 56.6452 36.365 56.7152C36.195 56.7952 36.025 56.8452 35.855 56.8952C35.545 56.9752 35.245 56.9852 34.975 56.9152L21.375 53.4652L26.565 58.4252L40.165 61.8752C40.435 61.9452 40.725 61.9252 41.045 61.8552C41.135 61.8352 41.225 61.8052 41.315 61.7752C41.395 61.7452 41.475 61.7152 41.555 61.6852C41.615 61.6552 41.675 61.6252 41.735 61.5952C41.795 61.5652 41.845 61.5352 41.905 61.5052C41.955 61.4752 42.005 61.4452 42.055 61.4152C42.055 42.0552 42.065 61.4152 42.065 61.4052C42.065 61.4052 42.065 61.4052 42.075 61.4052C42.115 61.3852 42.155 61.3552 42.195 61.3252C42.235 61.2952 42.285 61.2652 42.325 61.2352C42.365 61.2052 42.415 61.1752 42.455 61.1452C42.495 61.1152 42.535 61.0852 42.575 61.0552C42.615 61.0252 42.655 60.9852 42.695 60.9552C42.735 60.9252 42.775 60.8852 42.815 60.8552C42.855 60.8252 42.895 60.7852 42.935 60.7552C42.975 60.7152 43.015 60.6852 43.045 60.6452L43.055 60.6352C43.085 60.6052 43.125 60.5652 43.155 60.5352C43.195 60.4952 43.225 60.4552 43.265 60.4152C43.305 60.3752 43.335 60.3352 43.375 60.2952C43.415 60.2552 43.445 60.2052 43.485 60.1652C43.485 60.1652 43.485 60.1652 43.485 60.1552C43.485 60.1552 43.485 60.1552 43.485 60.1452C43.525 60.1052 43.555 60.0552 43.585 60.0152C43.625 59.9552 43.665 59.9052 43.705 59.8452C43.755 59.7752 43.795 59.7052 43.845 59.6352L43.855 59.6252C43.855 59.6252 43.855 59.6152 43.865 59.6152C43.975 59.4452 44.065 59.2652 44.145 59.0852C44.165 59.0352 44.185 58.9852 44.205 58.9352C44.255 58.8152 44.305 58.6852 44.345 58.5652C44.355 58.5452 44.355 58.5252 44.365 58.5052L49.145 40.2752L43.955 35.3252Z" fill="url(#paint3_linear_11_58)"/>
    <path d="M60.5579 14.5951L44.8179 14.6951L41.3878 0.805065C41.2078 0.0950652 40.5579 -0.124925 39.7579 0.065075C38.9579 0.255075 38.0278 0.865065 37.2778 1.80507L22.7678 20.0951L4.20782 28.4051C2.28782 29.2651 0.807872 31.7151 1.50787 32.8751L8.28784 44.0851L0.247863 63.1051C-0.422137 64.6951 0.337845 65.6251 1.66785 65.3051C1.97785 65.2251 2.31786 65.0851 2.68787 64.8651L21.3878 53.5051L34.9879 56.9551C35.2579 57.0251 35.5479 57.0051 35.8679 56.9351C37.2079 56.6051 38.7979 55.0951 39.1979 53.5751L43.9778 35.3451L60.4178 18.4451C62.0878 16.6951 62.1779 14.5851 60.5579 14.5951Z" fill="url(#paint4_linear_11_58)"/>
    <path opacity="0.7" d="M60.5579 14.5951L44.8179 14.6951L41.3878 0.805065C41.2078 0.0950652 40.5579 -0.124925 39.7579 0.065075C38.9579 0.255075 38.0278 0.865065 37.2778 1.80507L22.7678 20.0951L4.20782 28.4051C2.28782 29.2651 0.807872 31.7151 1.50787 32.8751L8.28784 44.0851L0.247863 63.1051C-0.422137 64.6951 0.337845 65.6251 1.66785 65.3051C1.97785 65.2251 2.31786 65.0851 2.68787 64.8651L21.3878 53.5051L34.9879 56.9551C35.2579 57.0251 35.5479 57.0051 35.8679 56.9351C37.2079 56.6051 38.7979 55.0951 39.1979 53.5751L43.9778 35.3451L60.4178 18.4451C62.0878 16.6951 62.1779 14.5851 60.5579 14.5951ZM60.1678 18.2151L43.6678 35.1751L38.8679 53.4851C38.5079 54.8351 37.0678 56.2951 35.7678 56.6151C35.6178 56.6551 35.4678 56.6751 35.3378 56.6751C35.2378 56.6751 35.1379 56.6651 35.0479 56.6451L21.3179 53.1651L2.50787 64.5951C2.17787 64.7951 1.86782 64.9351 1.57782 65.0051C1.42782 65.0451 1.28783 65.0651 1.14783 65.0651C0.927826 65.0651 0.617875 65.0051 0.447875 64.7451C0.237875 64.4251 0.267831 63.8751 0.527831 63.2451L8.63782 44.0651L1.77783 32.7151C1.61783 32.4451 1.60787 32.0351 1.75787 31.5551C2.10787 30.4151 3.19783 29.2251 4.33783 28.7151L22.9678 20.3751L37.5278 2.02507C38.1878 1.19507 39.0478 0.585077 39.8278 0.395077C39.9878 0.355077 40.1378 0.335064 40.2778 0.335064C40.7078 0.335064 40.9779 0.525077 41.0679 0.895077L44.5579 15.0251L60.5479 14.9251C60.9379 14.9251 61.1979 15.0651 61.3079 15.3451C61.5679 15.9151 61.2578 17.0951 60.1678 18.2151Z" fill="url(#paint5_linear_11_58)"/>
    <defs>
      <linearGradient id="paint0_linear_11_58" x1="48.6781" y1="5.46209" x2="40.859" y2="16.6998" gradientUnits="userSpaceOnUse">
        <stop offset="0.0394" stopColor="#FFC826"/>
        <stop offset="0.1112" stopColor="#FFA018"/>
        <stop offset="0.4264" stopColor="#F8771E"/>
        <stop offset="0.5129" stopColor="#EC6D1A"/>
        <stop offset="0.6677" stopColor="#CD510E"/>
        <stop offset="0.721" stopColor="#C14609"/>
      </linearGradient>
      <linearGradient id="paint1_linear_11_58" x1="-7.76537" y1="78.7405" x2="40.4935" y2="40.0716" gradientUnits="userSpaceOnUse">
        <stop offset="0.2285" stopColor="#FFC826"/>
        <stop offset="0.294" stopColor="#FFA018"/>
        <stop offset="0.4264" stopColor="#F8771E"/>
        <stop offset="0.5129" stopColor="#EC6D1A"/>
        <stop offset="0.6677" stopColor="#CD510E"/>
        <stop offset="0.721" stopColor="#C14609"/>
      </linearGradient>
      <linearGradient id="paint2_linear_11_58" x1="70.6589" y1="10.793" x2="44.3255" y2="41.4727" gradientUnits="userSpaceOnUse">
        <stop offset="0.0394" stopColor="#FFC826"/>
        <stop offset="0.2586" stopColor="#FFA018"/>
        <stop offset="0.3314" stopColor="#F8771E"/>
        <stop offset="0.4458" stopColor="#EC6D1A"/>
        <stop offset="0.6505" stopColor="#CD510E"/>
        <stop offset="0.721" stopColor="#C14609"/>
        <stop offset="0.8348" stopColor="#C3480A"/>
        <stop offset="0.8863" stopColor="#CA4E0D"/>
        <stop offset="0.9252" stopColor="#D75A11"/>
        <stop offset="0.9577" stopColor="#E96A18"/>
        <stop offset="0.977" stopColor="#F8771E"/>
        <stop offset="0.9864" stopColor="#F87A1E"/>
        <stop offset="0.992" stopColor="#FA821C"/>
        <stop offset="0.9967" stopColor="#FC901A"/>
        <stop offset="1" stopColor="#FFA018"/>
      </linearGradient>
      <linearGradient id="paint3_linear_11_58" x1="57.3352" y1="30.8003" x2="28.9566" y2="60.4574" gradientUnits="userSpaceOnUse">
        <stop offset="0.0394" stopColor="#FFC826"/>
        <stop offset="0.1112" stopColor="#FFA018"/>
        <stop offset="0.272" stopColor="#F8771E"/>
        <stop offset="0.3282" stopColor="#EF6F1B"/>
        <stop offset="0.567" stopColor="#CE510E"/>
        <stop offset="0.6973" stopColor="#C14609"/>
        <stop offset="0.7136" stopColor="#C64E0A"/>
        <stop offset="0.7382" stopColor="#D4620E"/>
        <stop offset="0.768" stopColor="#EC8413"/>
        <stop offset="0.7888" stopColor="#FFA018"/>
        <stop offset="0.7904" stopColor="#FE9E18"/>
        <stop offset="0.8278" stopColor="#E87F12"/>
        <stop offset="0.8666" stopColor="#D7660E"/>
        <stop offset="0.907" stopColor="#CB540B"/>
        <stop offset="0.95" stopColor="#C3490A"/>
        <stop offset="1" stopColor="#C14609"/>
      </linearGradient>
      <linearGradient id="paint4_linear_11_58" x1="11.2051" y1="22.1907" x2="43.4188" y2="51.3364" gradientUnits="userSpaceOnUse">
        <stop offset="0.0317" stopColor="#FCE878"/>
        <stop offset="0.0527" stopColor="#FCE676"/>
        <stop offset="0.6976" stopColor="#FEB432"/>
        <stop offset="1" stopColor="#FFA018"/>
      </linearGradient>
      <linearGradient id="paint5_linear_11_58" x1="44.7636" y1="53.6553" x2="12.8055" y2="22.2086" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFE6B9" stopOpacity="0"/>
        <stop offset="0.3821" stopColor="#FFF3DE" stopOpacity="0.4239"/>
        <stop offset="0.7037" stopColor="#FFFCF6" stopOpacity="0.7807"/>
        <stop offset="0.9014" stopColor="white"/>
      </linearGradient>
    </defs>
  </svg>
);

export const HeaderSection = ({
  title = "Trusted by parents,\npreferred by students,\nproven by results",
  subtitle = "Diploma in computerized accounting and finance – The perfect launchpad for a high growth career, shape a smarter future.",
  ctaText = "Let's Talk",
  ctaColor = "#ffc94b",
  studentImageSrc = "https://res.cloudinary.com/dobqxxtml/image/upload/v1753445588/student_with_book_fllooe.png",
  studentImageAlt = "Confident student with books showing thumbs up",
  imageWidth = "477px",
  imageHeight = "563px",
  imageTop = "250px",
  imageLeft = "797px"
}: HeaderSectionProps = {}): JSX.Element => {
  return (
    <div
      className="w-full min-h-screen relative overflow-hidden"
      style={{// color-bg
        background: 'radial-gradient(circle at 50% 50%, #3F43A9 0%, #0F1377 100%)'
      }}
      data-model-id="1:548-frame"
    >
      {/* Navigation */}
      <header className="container mx-auto py-6 px-4 flex justify-between items-center relative z-20">
        <div className="flex items-center">
        <div className="flex items-center space-x-2">
            <div className="w-[42px] h-14 rotate-0 relative">
              <svg width="43" height="56" viewBox="0 0 43 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M27.8959 37.9544C32.2025 37.7824 33.1578 39.9636 33.062 41.7814C32.7351 39.8214 29.6288 38.6437 25.6001 40.0593C22.3772 41.1917 23.7761 45.4946 24.756 47.4909L23.5583 46.2931C19.5511 41.7633 19.1301 37.4368 19.4205 35.8398C19.8197 36.239 19.8596 37.189 21.3805 38.1264C22.2857 38.6843 21.7354 38.047 21.6774 37.9544C19.7174 34.8241 20.7635 32.7352 21.199 32.1182C20.8724 35.9293 22.5423 38.1682 27.8959 37.9544Z" fill="#FFC350"/>
                <path d="M17.849 48.3829C10.2704 38.6264 15.8269 29.4735 19.5291 27.332C12.386 43.2733 27.924 52.1193 36.3085 53.8252C26.928 57.7812 20.0496 50.7748 17.849 48.3829Z" fill="#FFC350"/>
                <path d="M9.33461 43.7901C6.80839 30.3749 15.2744 23.8913 19.3396 23.3468C6.44713 36.9362 15.7449 50.9657 24.3557 55.7495C14.3379 56.7078 10.2057 48.3997 9.33461 43.7901Z" fill="#FFC350"/>
                <path d="M4.5134 30.0871C8.78185 21.376 17.0851 19.0174 20.0977 19.7434C4.33054 27.8448 6.94526 44.787 10.829 51.4292C2.20496 44.4603 1.40835 36.4239 4.5134 30.0871Z" fill="#FFC350"/>
                <path d="M3.64121 20.6121C8.95499 15.7339 18.7768 17.0551 23.0234 18.3254C4.81721 17.8025 1.06417 32.0456 1.46343 39.2325C-0.0247173 35.0583 -1.67257 25.4903 3.64121 20.6121Z" fill="#FFC350"/>
                <path d="M25.4114 22.3993C25.1572 22.3692 24.6874 21.9163 24.6525 21.9251C24.6177 21.9316 24.6346 21.9334 24.6659 21.96C25.9857 23.0794 27.1181 24.4927 28.8974 24.9152C29.7227 25.1112 31.0058 24.8477 31.8617 24.9152C32.0642 24.2401 31.4581 23.6817 31.1982 23.4434C30.0599 22.3993 27.8953 22.6107 25.6183 22.4204L25.4114 22.3993Z" fill="#FFC350"/>
                <path d="M31.2988 19.524C30.9504 13.9489 24.5559 9.69571 14.7862 11.0154C7.94567 11.5312 4.44681 15.284 2.98771 18.4351C8.56282 13.644 14.8203 13.6803 17.2522 14.2973C13.5935 14.1231 8.80964 15.379 4.63592 18.1299C11.5097 14.2973 22.3782 16.3992 24.5559 17.5607C24.5559 16.5154 22.5877 15.2773 21.8255 14.8417C26.7908 14.9289 30.2099 17.9995 31.2988 19.524Z" fill="#FFC350"/>
                <path d="M32.7156 19.8517C32.6285 17.4997 31.0619 16.2783 30.1545 15.1895C32.7674 16.2348 34.0951 17.928 34.5672 19.4162C35.5472 23.8806 38.0512 23.7717 40.7739 26.6028C42.9521 28.8677 41.9718 30.3777 41.2094 30.8495C41.4707 33.2886 40.8099 35.2777 40.4468 35.9673C41.2308 40.9326 36.4392 41.8584 34.0436 41.7495C34.6969 41.7132 36.5703 41.2375 38.0512 39.234C39.5321 37.2304 38.0875 35.0599 37.1801 34.2251C35.4379 35.8802 32.3164 35.568 30.9734 35.2051C31.8808 35.3503 34.2836 35.1833 36.6356 33.354C38.9876 31.5246 37.1801 29.6154 35.9823 28.8895C35.4596 27.0602 36.926 27.2562 37.7245 27.5828C39.641 28.8895 40.3379 28.9984 40.4468 28.8895C40.0112 26.9731 37.7245 25.3325 36.6356 24.7517C34.2836 23.8806 32.7156 20.786 32.7156 19.8517Z" fill="#FFC350"/>
                <path d="M30.7852 5.8513L27.3288 11.5211C20.1453 7.56619 12.6052 9.87785 9.73306 11.528L6.22088 5.8513C15.9293 11.8562 18.4448 4.45248 18.4891 1.07496e-06C19.3549 11.0043 27.0472 8.48601 30.7852 5.8513Z" fill="#FFC350"/>
              </svg>
            </div>
            <div className="ml-12">
              <div className="[font-family:'Reem_Kufi',Helvetica] font-normal text-white text-[11px] leading-[normal]">
                THE
              </div>
              <svg width="167" height="24" viewBox="0 0 167 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0.316406 22.9839V0.281473H0.346716L11.9859 16.8006L10.6219 16.5278L22.2308 0.281473H22.2914V22.9839H19.1391V7.46502L19.321 9.07147L11.1978 20.6197H11.1372L2.83216 9.07147L3.37775 7.58626V22.9839H0.316406Z" fill="white"/>
                <path d="M26.2223 22.9839L35.8913 0.0996094H36.0731L45.7421 22.9839H42.0746L35.0426 5.07051L37.3159 3.5853L29.3745 22.9839H26.2223Z" fill="white"/>
                <path d="M66.1971 21.0441C65.894 21.3067 65.4697 21.5694 64.9241 21.8321C64.3785 22.0948 63.7622 22.3373 63.0751 22.5596C62.4083 22.7616 61.7213 22.9233 61.014 23.0445C60.3068 23.1658 59.64 23.2264 59.0136 23.2264C56.6696 23.2264 54.6388 22.7616 52.9212 21.8321C51.2036 20.8824 49.8699 19.5993 48.9202 17.9827C47.9907 16.3662 47.5259 14.5273 47.5259 12.4662C47.5259 10.587 47.8189 8.91992 48.4049 7.46502C49.0112 6.01013 49.8396 4.78761 50.8904 3.79747C51.9411 2.80733 53.1334 2.05968 54.467 1.55451C55.8209 1.02913 57.2556 0.766437 58.7711 0.766437C60.0845 0.766437 61.2969 0.917989 62.4083 1.22109C63.5197 1.5242 64.4896 1.90813 65.3181 2.37289L64.2572 5.31299C63.8531 5.07051 63.3479 4.84823 62.7417 4.64616C62.1557 4.42388 61.5394 4.25213 60.8928 4.13089C60.2664 4.00964 59.6905 3.94902 59.1651 3.94902C57.4677 3.94902 55.9926 4.28244 54.7398 4.94926C53.487 5.61609 52.517 6.56582 51.83 7.79844C51.143 9.03106 50.7995 10.4961 50.7995 12.1934C50.7995 13.7494 51.143 15.1234 51.83 16.3156C52.5373 17.4876 53.5072 18.4071 54.7398 19.0739C55.9926 19.7407 57.4374 20.0741 59.0742 20.0741C59.8016 20.0741 60.5089 20.0034 61.1959 19.862C61.9031 19.7205 62.4992 19.5083 62.9842 19.2254V15.1638H58.7105V12.1025H66.1971V21.0441Z" fill="white"/>
                <path d="M90.8373 23.7417L73.8938 6.82851L74.8334 7.13161L74.894 22.9839H71.7115V0.311783H71.8327L88.564 17.225L87.8366 17.0431L87.7759 1.00892H90.9282V23.7417H90.8373Z" fill="white"/>
                <path d="M94.8941 22.9839L104.563 0.0996094H104.745L114.414 22.9839H110.746L103.714 5.07051L105.988 3.5853L98.0464 22.9839H94.8941Z" fill="white"/>
                <path d="M115.067 1.00892H129.707V4.07027H123.888V22.9839H120.705V4.07027H115.067V1.00892Z" fill="white"/>
                <path d="M134.315 1.00892H148.713V4.07027H137.468V10.4051H147.5V13.4665H137.468V19.9226H149.137V22.9839H134.315V1.00892Z" fill="white"/>
                <path d="M165.604 5.16144C164.836 4.7573 164.008 4.41378 163.118 4.13089C162.229 3.84799 161.371 3.70654 160.542 3.70654C159.39 3.70654 158.481 3.96923 157.814 4.49461C157.147 4.99978 156.814 5.70702 156.814 6.61633C156.814 7.24275 157.036 7.78833 157.481 8.25309C157.925 8.71785 158.501 9.12199 159.208 9.46551C159.916 9.80902 160.663 10.1323 161.451 10.4354C162.361 10.7587 163.24 11.173 164.088 11.6782C164.937 12.1631 165.634 12.8199 166.18 13.6483C166.725 14.4768 166.998 15.5882 166.998 16.9825C166.998 18.1343 166.705 19.185 166.119 20.1347C165.533 21.0845 164.695 21.8422 163.603 22.408C162.532 22.9536 161.239 23.2264 159.724 23.2264C158.309 23.2264 156.965 23.0041 155.692 22.5596C154.44 22.0948 153.359 21.5492 152.449 20.9228L153.874 18.4071C154.581 18.9526 155.45 19.4275 156.48 19.8316C157.511 20.2358 158.491 20.4378 159.421 20.4378C160.108 20.4378 160.774 20.3267 161.421 20.1044C162.068 19.862 162.603 19.4881 163.028 18.983C163.472 18.4778 163.694 17.811 163.694 16.9825C163.694 16.2954 163.502 15.7094 163.118 15.2245C162.735 14.7395 162.229 14.3253 161.603 13.9817C160.997 13.6382 160.34 13.3351 159.633 13.0724C158.946 12.8097 158.238 12.5269 157.511 12.2237C156.804 11.9004 156.147 11.5064 155.541 11.0416C154.955 10.5769 154.47 10.0111 154.086 9.34426C153.722 8.65723 153.54 7.82875 153.54 6.85882C153.54 5.66661 153.813 4.63606 154.359 3.76716C154.925 2.89826 155.703 2.21123 156.693 1.70606C157.683 1.20089 158.814 0.928092 160.087 0.887679C161.542 0.887679 162.815 1.06954 163.907 1.43327C164.998 1.77678 165.958 2.20113 166.786 2.7063L165.604 5.16144Z" fill="white"/>
              </svg>
            </div>
          </div>
        </div>
        <nav className="hidden md:flex items-center space-x-8">
          <a href="#" className="text-white hover:text-[#FFC350] border-b-2 border-white pb-1">Home</a>
          <a href="#" className="text-white hover:text-[#FFC350]">About Us</a>
          <Button className="bg-transparent hover:bg-transparent border border-white text-white rounded-md px-6">
            Contact Us
          </Button>
        </nav>
        <button className="md:hidden text-white">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="4" x2="20" y1="12" y2="12"/>
            <line x1="4" x2="20" y1="6" y2="6"/>
            <line x1="4" x2="20" y1="18" y2="18"/>
          </svg>
        </button>
      </header>

      {/* Hero Content - Two Column Layout */}
      <main className="container mx-auto px-4 pt-8 pb-16 flex flex-col lg:flex-row items-center min-h-[calc(100vh-120px)] relative z-10" role="main" aria-label="Hero section">
        {/* Left Content Block */}
        <div
          className="lg:w-1/2 mb-12 lg:mb-0 relative z-20 hero-overlap lg:min-h-[280px]"
          style={{
            transform: 'translateX(-120px)',
            marginRight: '100px',
            width: '800px',         
            maxWidth: '800px',      
            minHeight: '280px' 
          }}
        >
          <h1
            className="font-bold text-white leading-tight mb-8 text-5xl hero-title"
            style={{ fontSize: '56px' }}
          >
            {title.split('\n').map((line, index) => (
              <span key={index}>
                {line}
                {index < title.split('\n').length - 1 && <br />}
              </span>
            ))}
          </h1>
          <p className="text-lg text-white/80 mb-8 max-w-lg leading-relaxed">
            {subtitle}
          </p>
          <Button
            className="text-[#0b1131] font-semibold rounded-lg text-base transition-all hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-transparent"
            style={{
              backgroundColor: ctaColor,
              padding: '20px 30px'
            }}
            aria-label={`${ctaText} - Contact us to learn more about our programs`}
          >
            {ctaText}
          </Button>
        </div>

        {/* Right Image Block - Fixed Positioning */}
        {/* Image positioned absolutely at specific coordinates (477x563px at top:205px, left:797px) */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none z-10">
          <img
            src={studentImageSrc}
            alt={studentImageAlt}
            className="absolute pointer-events-auto hero-image bottom-0 lg:min-h-[280px]"
            style={{
              width: imageWidth,
              height: imageHeight,
              top: imageTop,
              left: imageLeft,
              objectFit: 'cover'
            }}
          />
        </div>
      </main>

      {/* Fixed Positioned Decorative Elements */}
      {/* CoinRight component - Fixed at 103.4px from right, 471.27px from bottom */}
      <div
        className="pointer-events-none z-15 animate-float"
        style={{
          position: 'absolute',
          right: '143px',
          bottom: '410px',
          width: 'auto',
          height: 'auto'
        }}
      >
        <CoinRight />
      </div>

      {/* CoinLeft component - Fixed at 691.64px from left, 56px from bottom */}
      <div
        className="pointer-events-none z-15 animate-float-delayed"
        style={{
          position: 'absolute',
          left: '900px',
          bottom: '1px',
          width: 'auto',
          height: 'auto'
        }}
      >
        <CoinLeft />
      </div>

      {/* Star component - Fixed at 55.08px from right, 277.69px from bottom with dimensions 66.92px × 70.31px */}
      <div
        className="pointer-events-none z-20 animate-float"
        style={{
          position: 'absolute',
          right: '155px',
          bottom: '207px',
          width: '66.92px',
          height: '70.31px',
          animationDelay: '0.5s'
        }}
      >
        <Star />
      </div>

      {/* Small Circle component - Fixed at 568.44px from right, 563.43px from bottom */}
      <div
        className="pointer-events-none z-15"
        style={{
          position: 'absolute',
          right: '918px',
          bottom: '683px',
          width: 'auto',
          height: 'auto'
        }}
      >
        <SmallCircle />
      </div>

      {/* Animation styles */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(2deg); }
        }
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
        .animate-float-delayed {
          animation: float-delayed 8s ease-in-out infinite;
          animation-delay: 1s;
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
          .hero-image {
            left: 600px !important;
            width: 400px !important;
            height: 472px !important;
          }
        }

        @media (max-width: 1024px) {
          .hero-image {
            position: relative !important;
            top: auto !important;
            left: auto !important;
            width: 350px !important;
            height: 413px !important;
            margin: 2rem auto 0 auto !important;
            display: block !important;
          }
        }

        @media (max-width: 768px) {
          .hero-title {
            font-size: 2.5rem !important;
          }
          .hero-overlap {
            transform: none !important;
            margin-right: 0 !important;
          }
          .hero-image {
            width: 300px !important;
            height: 354px !important;
            margin: 1.5rem auto 0 auto !important;
          }
        }

        @media (max-width: 640px) {
          .hero-title {
            font-size: 2rem !important;
          }
          .hero-image {
            width: 250px !important;
            height: 295px !important;
            margin: 1rem auto 0 auto !important;
          }
        }
      `}</style>
    </div>
  );
};